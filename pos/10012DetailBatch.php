<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * Created by PhpStorm.
 * File : 10012DetailBatch.php
 * Author: <EMAIL>
 * Date: 2018/11/19
 * Time: 11:55
 * Desc: 首页专题课卡片
 */
class Oplib_Pos_10012DetailBatch extends Oplib_Common_BasePos {

    public function invoke($arrCommand){

        $posId = intval(Oplib_Const_Operation::POS_PLAN_CARD);   //代理到10006
        $grade   = intval($arrCommand['grade']);
        $arrAdId = is_array($arrCommand['arrAdId']) ? $arrCommand['arrAdId'] : [];
        $arrOutput = array('info' => array());
        $arrPosInfo = Oplib_Common_Cache::getOperationInfoBatch($posId,$grade);

        foreach ($arrPosInfo as $adInfo){
            if(!in_array($adInfo['id'],$arrAdId)){
                continue;
            }
            $arrOutPut['list'][$adInfo['id']] = [
                'courseType' => $adInfo['courseType'],
                'name'       => $adInfo['name'],
                'skuDesc'    => $adInfo['skuDesc'],
                'skuBgImg'   => Hk_Util_Image::getImgUrlBySrc( $adInfo['ext']['skuBgImg']),
                'arrSkuId'   => $adInfo['arrSkuId'],
                'condition'  => $adInfo['condition'],
            ];

        }

        return $arrOutPut;

    }
}