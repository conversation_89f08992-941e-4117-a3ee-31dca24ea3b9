<?php
/***************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * Created by PhpStorm.
 * File : 11005DetailBatch.php
 * Author: <EMAIL>
 * Date: 2018/12/3
 * Time: 15:05
 * Desc: 学科页班课卡片（寒） | 19.01月版本
 */
class Oplib_Pos_11005DetailBatch extends Oplib_Common_BasePos {

    public function invoke($arrCommand){

        $posId = intval(Oplib_Const_Operation::POS_PLAN_CARD_XK);   //代理到11007
        $grade   = intval($arrCommand['grade']);
        $arrAdId = is_array($arrCommand['arrAdId']) ? $arrCommand['arrAdId'] : [];
        $arrOutput = array('info' => array());
        $arrPosInfo = Oplib_Common_Cache::getOperationInfoBatch($posId,$grade);

        foreach ($arrPosInfo as $adInfo){
            if(!in_array($adInfo['id'],$arrAdId)){
                continue;
            }
            $arrOutPut['list'][$adInfo['id']] = [
                'courseType'  => Oplib_Const_Operation::TYPE_PRIVATE_LONG,
                'subject'    => intval($adInfo['subject']),
                'name'       => $adInfo['name'],
                'skuDesc'    => $adInfo['skuDesc'],
                'skuBgImg'   => Hk_Util_Image::getImgUrlBySrc( $adInfo['ext']['skuBgImg']),
                'arrSkuId'   => $adInfo['arrSkuId'],
                'condition'  => $adInfo['condition'],
            ];

        }

        return $arrOutPut;
    }
}
