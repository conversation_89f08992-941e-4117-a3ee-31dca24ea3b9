<?php
/**
 * Created by PhpStorm.
 * User: <EMAIL>
 * Time: 2017/7/27 17:32
 */

class Zhibo_Dao_LmsLecture extends Hk_Common_BaseDao
{

    public function __construct() {
        $this->_dbName = 'fudao/zyb_fudao';
        $this->_db     = null;
        $this->_table  = "tblLmsLecture";
        $this->arrFieldsMap = array(
            'lectureId'  => 'lecture_id',
            'lectureType'=> 'lecture_type',
            'status'     => 'status',
            'subject'    => 'subject',
            'grade'      => 'grade',
            'operateId'  => 'operate_id',
            'operate'    => 'operate',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'deleted'    => 'deleted',
            'extData'    => 'ext_data',
        );

        $this->arrTypesMap = array(
            'lectureId'  => Hk_Service_Db::TYPE_INT,
            'lectureType'=> Hk_Service_Db::TYPE_INT,
            'status'     => Hk_Service_Db::TYPE_INT,
            'subject'    => Hk_Service_Db::TYPE_INT,
            'grade'      => Hk_Service_Db::TYPE_INT,
            'operateId'  => Hk_Service_Db::TYPE_INT,
            'operate'    => Hk_Service_Db::TYPE_STR,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'deleted'    => Hk_Service_Db::TYPE_INT,
            'extData'    => Hk_Service_Db::TYPE_JSON,
        );
    }
}