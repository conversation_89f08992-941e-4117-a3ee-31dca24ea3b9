<?php
/**
 * Copyright (c) 2016 zuoyebang.com, Inc. All Rights Reserved
 * Created by PhpStorm.
 * @file:Lecture.php
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>@zuoyebang.com
 * @date: 2016/7/1
 * @time: 17:09
 */

class Zhibo_Dao_Lecture extends Hk_Common_BaseDao
{

    public function __construct()
    {
        $this->_db = Hk_Service_Db::getDB(Zhibo_Static::DBCLUSTER_FUDAO, false, Zhibo_Static::DBLOG_FUDAO, Zhibo_Static::DBLOG_AUTOROTATE_FLAG);
        $this->_table = "tblLecture";

        $this->arrFieldsMap = array(
            'lectureId' => 'lecture_id',
            'lectureName' => 'lecture_name',
            'baseLectureId' => 'base_lecture_id',
            'type' => 'type',
            'gradeId' => 'grade_id',
            'subjectId' => 'subject_id',
            'content' => 'content',
            'authorUid' => 'author_uid',
            'authorName' => 'author_name',
            'status' => 'status',
            'createTime' => 'create_time',
            'updateTime' => 'update_time',
            'extData' => 'ext_data',
        );

        $this->arrTypesMap = array(
            'lectureId' => Hk_Service_Db::TYPE_INT,
            'type' => Hk_Service_Db::TYPE_INT,
            'gradeId' => Hk_Service_Db::TYPE_INT,
            'subjectId' => Hk_Service_Db::TYPE_INT,
            'authorUid' => Hk_Service_Db::TYPE_INT,
            'status' => Hk_Service_Db::TYPE_INT,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'updateTime' => Hk_Service_Db::TYPE_INT,
            'extData' => Hk_Service_Db::TYPE_JSON,
        );
    }
}