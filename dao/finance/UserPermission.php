<?php

class Qdlib_Dao_Finance_UserPermission extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_CHANNEL;
        $this->_db = null;
        $this->_logFile = Qdlib_Util_DB::DBLOG_QUDAO;
        $this->_table = "sk_user_permission";

        $this->arrFieldsMap = array(
            'id'           => 'id',
            'userId'       => 'user_id',
            'permissionId' => 'permission_id',
        );

        $this->arrTypesMap = array(
            'id'            => Hk_Service_Db::TYPE_INT,
            'userId'        => Hk_Service_Db::TYPE_INT,
            'permissionId'  => Hk_Service_Db::TYPE_INT,
        );
    }

    public function getAllFields() {
        return array_keys($this->arrFieldsMap);
    }

    public function reconnect() {
        if(empty($this->_db)) {
            $this->_db     = Qdlib_Util_DB::getQudaoDb(false);
        }
        Qdlib_Util_DB::reconnect($this->_db);
    }
}
