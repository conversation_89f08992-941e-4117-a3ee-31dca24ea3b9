<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file BatchCoupon.php
 * <AUTHOR>
 * @date 2015/11/17 13:26:46
 * @brief 批量优惠券报名记录
 *  
 **/

class Hkzb_Dao_Fudao_BatchCoupon extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = 'fudao/zyb_fudao';
        //$this->_db     = Hk_Service_Db::getDB($this->_dbName);
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblBatchCoupon";
        $this->arrFieldsMap = array(
            'id'          => 'id',
            'purpose'     => 'purpose',
            'unit'        => 'unit',
            'discount'    => 'discount',
            'startTime'   => 'start_time',
            'endTime'     => 'end_time',
            'rule'        => 'rule',
            'num'         => 'num',
            'studentNum'  => 'student_num',
            'fileName'    => 'file_name',
            'createTime'  => 'create_time',
            'updateTime'  => 'update_time',
            'operatorUid' => 'operator_uid',
            'operator'    => 'operator',
            'extData'     => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'          => Hk_Service_Db::TYPE_INT,
            'purpose'     => Hk_Service_Db::TYPE_STR,
            'unit'        => Hk_Service_Db::TYPE_INT,
            'discount'    => Hk_Service_Db::TYPE_INT,
            'startTime'   => Hk_Service_Db::TYPE_INT,
            'endTime'     => Hk_Service_Db::TYPE_INT,
            'rule'        => Hk_Service_Db::TYPE_STR,
            'num'         => Hk_Service_Db::TYPE_INT,
            'studentNum'  => Hk_Service_Db::TYPE_INT,
            'fileName'    => Hk_Service_Db::TYPE_STR,
            'createTime'  => Hk_Service_Db::TYPE_INT,
            'updateTime'  => Hk_Service_Db::TYPE_INT,
            'operatorUid' => Hk_Service_Db::TYPE_INT,
            'operator'    => Hk_Service_Db::TYPE_STR,
            'extData'     => Hk_Service_Db::TYPE_JSON,
        );
    }
}