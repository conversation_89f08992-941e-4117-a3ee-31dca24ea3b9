<?php


/**
 * @file    ExamPaperRelation.php
 * <AUTHOR>
 * @data    2017-09-06
 * @desc    试卷绑定关系
 *
 */
class Hkzb_Dao_Fudao_Exam_ExamPaperRelation extends Hk_Common_BaseDao {

    public function __construct() {
        $this->_dbName      = 'resource/fudao_teachresource';
        $this->_db          = null;
        $this->_table       = "tblExamPaperRelation";
        $this->arrFieldsMap = array(
            'id'           => 'id',
            'examId'       => 'exam_id',
            'courseId'     => 'course_id',
            'lessonId'     => 'lesson_id',
            'teacherUid'   => 'teacher_uid',
            'relationType' => 'relation_type',
            'status'       => 'status',
            'examInfo'     => 'exam_info',
            'extData'      => 'ext_data',
            'operatorUid'  => 'operator_uid',
            'operatorName' => 'operator_name',
            'createTime'   => 'create_time',
            'updateTime'   => 'update_time',
        );

        $this->arrTypesMap = array(
            'id'           => Hk_Service_Db::TYPE_INT,
            'examId'       => Hk_Service_Db::TYPE_INT,
            'courseId'     => Hk_Service_Db::TYPE_INT,
            'lessonId'     => Hk_Service_Db::TYPE_INT,
            'teacherUid'   => Hk_Service_Db::TYPE_INT,
            'relationType' => Hk_Service_Db::TYPE_INT,
            'status'       => Hk_Service_Db::TYPE_INT,
            'extData'      => Hk_Service_Db::TYPE_JSON,
            'examInfo'     => Hk_Service_Db::TYPE_JSON,
            'operatorUid'  => Hk_Service_Db::TYPE_INT,
            'operatorName' => Hk_Service_Db::TYPE_STR,
            'createTime'   => Hk_Service_Db::TYPE_INT,
            'updateTime'   => Hk_Service_Db::TYPE_INT,
        );
    }

}