<?php

/**
 * @file   LessonConfig.php
 * <AUTHOR>
 * @date   2017-11-16
 * @brief  章节直播配置
 *
 **/
class Hkzb_Dao_Fudao_LessonConfig extends Hk_Common_BaseDao {
    public function __construct() {
//        $this->_dbName      = 'fudao/zyb_fudao';
        $this->_dbName      = 'jxzt_teacher/jxzt_teacher';
        $this->_db          = null;
        $this->_table       = "tblLessonConfig";
        $this->arrFieldsMap = array(
            'id'          => 'id',
            'lessonId'    => 'lesson_id',
            'courseId'    => 'course_id',
            'grade'       => 'grade',
            'subject'     => 'subject',
            'teacherUid'  => 'teacher_uid',
            'ip'          => 'ip',
            'versionCode' => 'version_code',
            'wireless'    => 'wireless',
            'deviceInfo'  => 'device_info',
            'videoConfig' => 'video_config',
            'extData'     => 'ext_data',
            'createTime'  => 'create_time',
            'updateTime'  => 'update_time',
        );

        $this->arrTypesMap = array(
            'id'          => Hk_Service_Db::TYPE_INT,
            'lessonId'    => Hk_Service_Db::TYPE_INT,
            'courseId'    => Hk_Service_Db::TYPE_INT,
            'grade'       => Hk_Service_Db::TYPE_INT,
            'subject'     => Hk_Service_Db::TYPE_INT,
            'teacherUid'  => Hk_Service_Db::TYPE_INT,
            'ip'          => Hk_Service_Db::TYPE_STR,
            'versionCode' => Hk_Service_Db::TYPE_INT,
            'wireless'    => Hk_Service_Db::TYPE_INT,
            'deviceInfo'  => Hk_Service_Db::TYPE_JSON,
            'videoConfig' => Hk_Service_Db::TYPE_JSON,
            'extData'     => Hk_Service_Db::TYPE_JSON,
            'createTime'  => Hk_Service_Db::TYPE_INT,
            'updateTime'  => Hk_Service_Db::TYPE_INT,
        );
    }
}