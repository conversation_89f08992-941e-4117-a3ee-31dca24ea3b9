<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file TradeRecord.php
 * <AUTHOR>
 * @date 2015/11/17 13:26:46
 * @brief 交易记录
 *  
 **/

class Hkzb_Dao_Fudao_TradeRecord extends Hk_Common_BaseMultiDao
{
    //当前分表数量
    const TABLE_NUM = 30;

    public function __construct()
    {
        $this->_dbName      = 'fudao/zyb_fudao';
        $this->_db          = null;
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table       = "tblTradeRecord";
        $this->_tableName   = "tblTradeRecord";
        $this->_partionKey  = 'orderId';
        $this->_partionType = self::TYPE_TABLE_PARTION_MUL;
        $this->_partionNum  = 10000000;
        $this->arrFieldsMap = array(
            'id'            => 'id',
            'orderId'       => 'trade_record_id',
            'parentOrderId' => 'trade_record_parent_id',
            'type'          => 'type',
            'payOrderId'    => 'order_id',
            'studentUid'    => 'student_uid',
            'courseId'      => 'course_id',
            'grade'         => 'grade',
            'subject'       => 'subject',
            'teacherUid'    => 'teacher_uid',
            'assistantUid'  => 'assistant_uid',
            'price'         => 'price',
            'payment'       => 'payment',
            'status'        => 'status',
            'sendStatus'    => 'send_status',
            'createTime'    => 'create_time',
            'updateTime'    => 'update_time',
            'extData'       => 'ext_data',
        );

        $this->arrTypesMap = array(
            'id'            => Hk_Service_Db::TYPE_INT,
            'orderId'       => Hk_Service_Db::TYPE_INT,
            'parentOrderId' => Hk_Service_Db::TYPE_INT,
            'type'          => Hk_Service_Db::TYPE_INT,
            'payOrderId'    => Hk_Service_Db::TYPE_INT,
            'studentUid'    => Hk_Service_Db::TYPE_INT,
            'courseId'      => Hk_Service_Db::TYPE_INT,
            'grade'         => Hk_Service_Db::TYPE_INT,
            'subject'       => Hk_Service_Db::TYPE_INT,
            'teacherUid'    => Hk_Service_Db::TYPE_INT,
            'assistantUid'  => Hk_Service_Db::TYPE_INT,
            'price'         => Hk_Service_Db::TYPE_INT,
            'payment'       => Hk_Service_Db::TYPE_INT,
            'status'        => Hk_Service_Db::TYPE_INT,
            'sendStatus'    => Hk_Service_Db::TYPE_INT,
            'createTime'    => Hk_Service_Db::TYPE_INT,
            'updateTime'    => Hk_Service_Db::TYPE_INT,
            'extData'       => Hk_Service_Db::TYPE_JSON,
        );
    }
}