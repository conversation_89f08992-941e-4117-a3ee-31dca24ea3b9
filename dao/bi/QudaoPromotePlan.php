<?php

class Qdlib_Dao_Bi_QudaoPromotePlan extends Hk_Common_BaseDao {

    public function __construct() {
        $this->_dbName  = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db      = null;
        $this->_logFile = Qdlib_Util_DB::DBLOG_QUDAO;

        $this->_table = "tblBiQudaoPromotePlan";

        $this->arrFieldsMap = [
            'id'             => 'id',
            'channel'        => 'channel',
            'promoteSign'    => 'promote_sign',
            'frPrefix'       => 'frPrefix',
            'planNumMin'     => 'plan_num_min',
            'planNumMax'     => 'plan_num_max',
            'promoteType'    => 'promote_type',
            'account'        => 'account',
            'attrClassType'  => 'attr_class_type',
            'attrLxWay'      => 'attr_lx_way',
            'attrSubject'    => 'attr_subject',
            'attrPageType'   => 'attr_page_type',
            'attrPageGroupId'=> 'attr_page_group_id',
            'businessLine'   => 'business_line',
            'attrProject'    => 'attr_project',
            'deliveryForm'   => 'delivery_form',
            'agentId'        => 'agent_id',
        ];

        $this->arrTypesMap = [
            'id'             => Hk_Service_Db::TYPE_INT,
            'channel'        => Hk_Service_Db::TYPE_STR,
            'promoteSign'    => Hk_Service_Db::TYPE_STR,
            'frPrefix'       => Hk_Service_Db::TYPE_STR,
            'planNumMin'     => Hk_Service_Db::TYPE_INT,
            'planNumMax'     => Hk_Service_Db::TYPE_INT,
            'promoteType'    => Hk_Service_Db::TYPE_INT,
            'account'        => Hk_Service_Db::TYPE_STR,
            'attrClassType'  => Hk_Service_Db::TYPE_STR,
            'attrLxWay'      => Hk_Service_Db::TYPE_STR,
            'attrSubject'    => Hk_Service_Db::TYPE_STR,
            'attrPageType'   => Hk_Service_Db::TYPE_STR,
            'attrPageGroupId'=> Hk_Service_Db::TYPE_INT,
            'businessLine'   => Hk_Service_Db::TYPE_STR,
            'attrProject'    => Hk_Service_Db::TYPE_STR,
            'deliveryForm'   => Hk_Service_Db::TYPE_STR,
            'agentId'        => Hk_Service_Db::TYPE_INT,
        ];
    }

    public function getAllFields() {
        return array_keys($this->arrFieldsMap);
    }

    public function reconnect() {
        if(empty($this->_db)) {
            $this->_db     = Qdlib_Util_DB::getQudaoDb(false);
        }
        Qdlib_Util_DB::reconnect($this->_db);
    }

    public function __get($name) {
        return $this->$name;
    }
}
