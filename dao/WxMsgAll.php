<?php

/**
 * Created by Growth AutoCode.
 * User: <EMAIL>
 * Date: 2020/01/13
 * Time: 15:25
 */
class Qdlib_Dao_WxMsgAll extends Hk_Common_BaseDao
{
    public function __construct()
    {
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_table = "tblWxMsgAll";

        $this->arrFieldsMap = array(
            'id'             => 'id',
            'title'          => 'title',
            'appidStr'       => 'appid_str',
            'sendType'       => 'send_type',
            'sendTime'       => 'send_time',
            'status'         => 'status',
            'deleted'        => 'deleted',
            'errNum'         => 'err_num',
            'content'        => 'content',
            'pageUpdateTime' => 'page_update_time',
            'createTime'     => 'create_time',
            'updateTime'     => 'update_time',

        );

        $this->arrTypesMap = array(
            'id'             => Hk_Service_Db::TYPE_INT,
            'title'          => Hk_Service_Db::TYPE_STR,
            'appidStr'       => Hk_Service_Db::TYPE_JSON,
            'sendType'       => Hk_Service_Db::TYPE_INT,
            'sendTime'       => Hk_Service_Db::TYPE_INT,
            'status'         => Hk_Service_Db::TYPE_INT,
            'deleted'        => Hk_Service_Db::TYPE_INT,
            'errNum'         => Hk_Service_Db::TYPE_INT,
            'content'        => Hk_Service_Db::TYPE_STR,
            'pageUpdateTime' => Hk_Service_Db::TYPE_INT,
            'createTime'     => Hk_Service_Db::TYPE_INT,
            'updateTime'     => Hk_Service_Db::TYPE_INT,

        );
    }
}