<?php

class Qdlib_Dao_QudaoOrderCommit extends Hk_Common_BaseDao
{

    public function __construct()
    {
        $this->_dbName = Qdlib_Util_DB::DBCLUSTER_QUDAO;
        $this->_db = null;
        $this->_logFile = Qdlib_Util_DB::DBLOG_QUDAO;

        $this->_table = "tblTransformOrderCommit";
        $this->arrFieldsMap = array(
            'id'         => 'id',
            'dataSource' => 'data_source',
            'account'    => 'account',
            'channel'    => 'channel',
            'orderId'    => 'order_id',
            'subOrderId' => 'sub_order_id',
            'clickId'    => 'click_id',
            'showId'     => 'show_id',
            'adSign'     => 'ad_sign',
            'oriFrom'    => 'orifrom',
            'createTime' => 'create_time',
            'deleted'    => 'deleted',
            'extData'    => 'ext_data',
            'uid'        => 'uid',
            'callbiStatus'  => 'callbi_status',
            'statStatus'    => 'stat_status',
            'statData'      => 'stat_data',
        );

        $this->arrTypesMap = array(
            'id'         => Hk_Service_Db::TYPE_INT,
            'account'    => Hk_Service_Db::TYPE_STR,
            'channel'    => Hk_Service_Db::TYPE_STR,
            'dataSource' => Hk_Service_Db::TYPE_STR,
            'orderId'    => Hk_Service_Db::TYPE_INT,
            'subOrderId' => Hk_Service_Db::TYPE_INT,
            'clickId'    => Hk_Service_Db::TYPE_STR,
            'showId'     => Hk_Service_Db::TYPE_STR,
            'adSign'     => Hk_Service_Db::TYPE_STR,
            'oriFrom'    => Hk_Service_Db::TYPE_STR,
            'createTime' => Hk_Service_Db::TYPE_INT,
            'deleted'    => Hk_Service_Db::TYPE_INT,
            'extData'    => Hk_Service_Db::TYPE_JSON,
            'uid'        => Hk_Service_Db::TYPE_INT,
            'callbiStatus'  =>  Hk_Service_Db::TYPE_INT,
            'statStatus'    =>  Hk_Service_Db::TYPE_INT,
            'statData'      =>  Hk_Service_Db::TYPE_JSON,
        );
    }

    public function getAllFields() {
        return array_keys($this->arrFieldsMap);
    }

    public function __get($name) {
        return $this->$name;
    }

    public function reconnect() {
        if(empty($this->_db)) {
            $this->_db     = Qdlib_Util_DB::getQudaoDb(false);
        }
        Qdlib_Util_DB::reconnect($this->_db);
    }

}