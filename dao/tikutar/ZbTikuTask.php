<?php
/**
 * @file ZbTikuTask.php.
 * <AUTHOR>
 * @date: 2018/07/02
 */
class Hkzb_Dao_TikuTar_ZbTikuTask extends Hk_Common_BaseDao
{
    public $arrFields;
    public function __construct() {
        $this->_logFile     = Hkzb_Util_FuDao::DBLOG_FUDAO;
        $this->_dbName = 'resource/fudao_teachresource';
        $this->_db     = Hk_Service_Db::getDB($this->_dbName);
        $this->_table  = "tblZbTikuTask";
        $this->arrFieldsMap = array(
            'taskId'          => 'task_id',
            'taskName'        => 'task_name',
            'belongProjectId' => 'belong_project_id',
            'fileNamePid'     => 'file_name_pid',
            'fileName'        => 'file_name',
            'executorUid'     => 'executor_uid',
            'executor'        => 'executor',
            'priority'        => 'priority',
            'homeworkNum'     => 'homework_num',
            'remark'          => 'remark',
            'status'          => 'status',
            'deleted'         => 'deleted',
            'ext'             => 'ext',
            'createTime'      => 'create_time',
            'updateTime'      => 'update_time',
            'createName'      => 'create_name',
            'updateName'      => 'update_name',
            'createUid'       => 'create_uid',
            'updateUid'       => 'update_uid',
        );

        $this->arrTypesMap = array(
            'taskId'          => Hk_Service_Db::TYPE_INT,
            'taskName'        => Hk_Service_Db::TYPE_STR,
            'belongProjectId' => Hk_Service_Db::TYPE_INT,
            'fileNamePid'     => Hk_Service_Db::TYPE_STR,
            'fileName'        => Hk_Service_Db::TYPE_STR,
            'executorUid'     => Hk_Service_Db::TYPE_INT,
            'executor'        => Hk_Service_Db::TYPE_STR,
            'priority'        => Hk_Service_Db::TYPE_INT,
            'status'          => Hk_Service_Db::TYPE_INT,
            'deleted'         => Hk_Service_Db::TYPE_INT,
            'ext'             => Hk_Service_Db::TYPE_JSON,
            'createTime'      => Hk_Service_Db::TYPE_INT,
            'updateTime'      => Hk_Service_Db::TYPE_INT,
            'createName'      => Hk_Service_Db::TYPE_STR,
            'updateName'      => Hk_Service_Db::TYPE_STR,
            'createUid'       => Hk_Service_Db::TYPE_INT,
            'updateUid'       => Hk_Service_Db::TYPE_INT,
        );

        $this->arrFields = array_keys($this->arrFieldsMap);
    }
}