<?php
    class Bd_Crypt_Ucrypt
    {
        const CKEY = 'BaiduCrypt';
        public static function & reinterpret_cast($intUserID)
		{
			$arrValue = array();
			$arrValue[] = $intUserID & 0x000000ff;
			$arrValue[] = ($intUserID & 0x0000ff00) >> 8;
			$arrValue[] = ($intUserID & 0x00ff0000) >> 16;
			$arrValue[] = ($intUserID >> 24) & 0x000000ff;
            
            return $arrValue;
        }

        static function id_encode($id, $key = CKEY)
        {
            $crypt = new Bd_Crypt_CFcrypt($key);
            return $crypt->getEncodeStrByPicId($id);
        }

        static function id_decode($string, $key = CKEY)
        {
            $id = 0;
            $crypt = new Bd_Crypt_CFcrypt($key);
            $crypt->getPicIdByEncodeStr($string, $id);
            return $id;
        }

        public static function ucrypt_encode($intUserID, $strUserName = '')
        {
		$strChars = '0123456789abcdef';
		$is_bigInt = false;
		if($intUserID > 0xffffffff)
		{
			$is_bigInt = true;
			$arrValue_high = self::reinterpret_cast($intUserID >> 32);
		}

		$arrValue_low = self::reinterpret_cast($intUserID);

		$strCode = $strChars[$arrValue_low[0] >> 4] . $strChars[$arrValue_low[0] & 15];
		$strCode .= $strChars[$arrValue_low[1] >> 4] . $strChars[$arrValue_low[1] & 15];

		$intLen = strlen($strUserName);

		for( $i = 0; $i < $intLen; ++$i )
		{
			$intValue = ord($strUserName[$i]);
			$strCode .= $strChars[($intValue >> 4)] . $strChars[($intValue & 15)];

		}

		if($is_bigInt){
			//分隔符
			$strCode .= "g";
			$strCode .= $strChars[$arrValue_low[2] >> 4] . $strChars[$arrValue_low[2] & 15];
			$strCode .= $strChars[$arrValue_low[3] >> 4] . $strChars[$arrValue_low[3] & 15];
			$strCode .= $strChars[$arrValue_high[0] >> 4] . $strChars[$arrValue_high[0] & 15];
			$strCode .= $strChars[$arrValue_high[1] >> 4] . $strChars[$arrValue_high[1] & 15];
			$strCode .= $strChars[$arrValue_high[2] >> 4] . $strChars[$arrValue_high[2] & 15];
			$strCode .= $strChars[$arrValue_high[3] >> 4] . $strChars[$arrValue_high[3] & 15];
		}else{

			$strCode .= $strChars[$arrValue_low[2] >> 4] . $strChars[$arrValue_low[2] & 15];
			$strCode .= $strChars[$arrValue_low[3] >> 4] . $strChars[$arrValue_low[3] & 15];
		}

            return $strCode;
        }


	/**
	 * @param string crypt string
	 * @param bool use username or not
	 * @return faild ==> false, success ==> array(uid, uname)
	 *
	 **/
	public static function ucrypt_decode($strCode, $bolNeedUserName = false) //解密
	{
		$intLen = strlen($strCode);

		if( $intLen < 8 ) //错误
		{
			return false;
		}

		$intUserID = hexdec($strCode[$intLen - 2] . $strCode[$intLen - 1]);
		$intUserID = ($intUserID << 8) + hexdec($strCode[$intLen - 4] . $strCode[$intLen - 3]);
		$intLast = strpos($strCode, "g");
		if($intLast){
			$intUserID = ($intUserID << 8) + hexdec($strCode[$intLen - 6] . $strCode[$intLen - 5]);
			$intUserID = ($intUserID << 8) + hexdec($strCode[$intLen - 8] . $strCode[$intLen - 7]);
			$intUserID = ($intUserID << 8) + hexdec($strCode[$intLen - 10] . $strCode[$intLen - 9]);
			$intUserID = ($intUserID << 8) + hexdec($strCode[$intLen - 12] . $strCode[$intLen - 11]);
		}

		$intUserID = ($intUserID << 8) + hexdec($strCode[2] . $strCode[3]);
		$intUserID = ($intUserID << 8) + hexdec($strCode[0] . $strCode[1]);

		if( $bolNeedUserName )   //需要解密出用户名
		{
			if($intLast <= 0){
				$intLast = $intLen -4;
			}
			$strUserName = '';
			for( $i = 4; $i < $intLast; $i += 2 )
			{
				$strUserName .= chr(hexdec($strCode[$i] . $strCode[$i + 1]));
			}
			if( strlen($strUserName) > 32 || !preg_match('/^[^<>"\'\/]+$/', $strUserName) )
			{
				return false;
			}
	
			return array (
				'user_id'  =>  $intUserID,
				'user_name'=>  $strUserName,
			);

		}
		else
		{
			return $intUserID;
		}
	}    
    }
