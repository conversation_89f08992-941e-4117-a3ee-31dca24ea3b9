<?php

/**
 * @file   Cpu.php
 * <AUTHOR>
 * @date   2019/1/9
 * @brief  课程产品常量
 */

class Zb_Const_Cpu {

    //业务线 详见 http://ued.zuoyebang.cc/documents/docs/dds/source.html
    const ZYB_FUDAO          = 4;//一课
    const ZYB_DIYOU_XIZI     = 101;//鸭鸭低幼-习字
    const ZYB_DIYOU_YINGYU   = 102;//鸭鸭低幼-英语
    const ZYB_DIYOU_YUWEN    = 103;//鸭鸭低幼-语文
    const ZYB_ASSISTANT      = 111;//内部培训
    public static $SOURCE_DESC_MAP = array(
        self::ZYB_FUDAO              => '一课',
        self::ZYB_DIYOU_XIZI         => '鸭鸭低幼-习字',
        self::ZYB_DIYOU_YINGYU       => '鸭鸭低幼-英语',
        self::ZYB_DIYOU_YUWEN        => '鸭鸭低幼-语文',
        self::ZYB_ASSISTANT          => '内部培训',
    );
    public static $YY_SOURCE_DESC_MAP = array(
        self::ZYB_DIYOU_XIZI,    //'鸭鸭低幼-习字',
        self::ZYB_DIYOU_YINGYU,  //'鸭鸭低幼-英语',
        self::ZYB_DIYOU_YUWEN,   //'鸭鸭低幼-语文',
    );

    /**
     * 课程产品类型
     */

    //课程类型 -- 专题课
    const TYPE_PRIVATE = 0;
    //课程类型 -- 班课
    const TYPE_PRIVATE_LONG = 2;
    //课程类型 -- 预备班
    const TYPE_PRE_LONG = 4;
    //课程类型 -- 短训班
    const SHORT_TRAINING = 12;
    //帮帮英语低幼
    const TYPE_BB_CHILD = 6;
    //课程类型 -- 鸭鸭写字正价课
    const TYPE_YY_WRITE_CLASS = 7;
    //课程类型 -- 鸭鸭写字体验课
    const TYPE_YY_WRITE_EXPERIENCE = 8;
    //课程类型 -- 鸭鸭英语周课
    const TYPE_YY_ENGLISH_WEEK = 9;
    //课程类型 -- 鸭鸭英语双周课
    const TYPE_YY_ENGLISH_TWO_WEEK = 10;
    //课程类型 -- 鸭鸭英语年课
    const TYPE_YY_ENGLISH_YEAR = 11;
    //课程类型 -- 鸭鸭语文周课
    const TYPE_YY_CHINESE_WEEK = 14;
    //课程类型 -- 鸭鸭语文双周课
    const TYPE_YY_CHINESE_TWO_WEEK = 15;
    //课程类型 -- 鸭鸭语文月课
    const TYPE_YY_CHINESE_MONTH = 16;
    //课程类型 -- 鸭鸭语文半年课
    const TYPE_YY_CHINESE_HALF_YEAR = 17;
    //课程类型 -- 鸭鸭语文年课
    const TYPE_YY_CHINESE_YEAR = 18;

    public static $TYPE_DESC_MAP = array(
        self::TYPE_PRIVATE                => '专题课',
        self::TYPE_PRIVATE_LONG           => '班课',
        self::TYPE_PRE_LONG               => '试听课',
        self::TYPE_BB_CHILD               => '帮帮英语低幼',
        self::TYPE_YY_WRITE_CLASS         => '鸭鸭写字正价课',
        self::TYPE_YY_WRITE_EXPERIENCE    => '鸭鸭写字体验课',
        self::TYPE_YY_ENGLISH_WEEK        => '鸭鸭英语周课',
        self::TYPE_YY_ENGLISH_TWO_WEEK    => '鸭鸭英语双周课',
        self::TYPE_YY_ENGLISH_YEAR        => '鸭鸭英语年课',
        self::TYPE_YY_CHINESE_WEEK        => '鸭鸭语文周课',
        self::TYPE_YY_CHINESE_TWO_WEEK    => '鸭鸭语文双周课',
        self::TYPE_YY_CHINESE_MONTH       => '鸭鸭语文月课',
        self::TYPE_YY_CHINESE_HALF_YEAR   => '鸭鸭语文半年课',
        self::TYPE_YY_CHINESE_YEAR        => '鸭鸭语文年课',
    );


    /**
     * 课程产品状态
     */
    const STATUS_UNRELEASE = 0; // 未发布
    const STATUS_RELEASEING = 3; // 发布中
    const STATUS_CONFIGING = 1; // 配置中
    const STATUS_FINISHED = 2; // 已完成

    public static $STATUS_DESC_MAP = array(
        self::STATUS_UNRELEASE => '未发布',
        self::STATUS_RELEASEING => '发布中',
        self::STATUS_CONFIGING => '配置中',
        self::STATUS_FINISHED => '已完成',
    );


    /**
     * 课程产品删除状态
     */
    const UNDELETED = 0; // 未删除
    const DELETED = 1; // 已删除

    public static $DELETED_DESC_MAP = array(
        self::UNDELETED => '未删除',
        self::DELETED => '已删除',
    );


    /**
     * 课程产品服务项
     */
    const SERVICE_MATERIAL = 19; // 教辅服务
    const SERVICE_FORCED_PRE_EXAM = 30; // 强制报前测
    const SERVICE_UNFORCED_PRE_EXAM = 31; // 非强制报前测
    const SERVICE_POST_EXAM = 32; // 报后测

    public static $SERVICE_DESC_MAP = array(
        self::SERVICE_MATERIAL => '教辅服务',
        self::SERVICE_FORCED_PRE_EXAM => '强制报前测',
        self::SERVICE_UNFORCED_PRE_EXAM => '非强制报前测',
        self::SERVICE_POST_EXAM => '报后测',
    );

    public static $SERVICE_EXAM_DESC_MAP = array(
        self::SERVICE_FORCED_PRE_EXAM => '强制报前测',
        self::SERVICE_UNFORCED_PRE_EXAM => '非强制报前测',
        self::SERVICE_POST_EXAM => '报后测',
    );


    /**
     * 课程产品锁定状态
     */
    const UNLOCKED = 0; // 未锁定
    const LOCKED = 1; // 已锁定

    public static $LOCKED_DESC_MAP = array(
        self::UNLOCKED => '未锁定',
        self::LOCKED => '已锁定',
    );


    /**
     * 课程产品品牌
     */
    const BRAND_ID_YIKE = 1; // 一课
    const BRAND_ID_HUANXIONG = 2; // 浣熊
    const BRAND_ID_BANGBANG = 3; // 帮帮
    const BRAND_ID_YAYA = 4; // 鸭鸭

    public static $BRAND_DESC_MAP = array(
        self::BRAND_ID_YIKE => '一课',
        self::BRAND_ID_HUANXIONG => '浣熊',
        self::BRAND_ID_BANGBANG => '帮帮',
        self::BRAND_ID_YAYA => '鸭鸭',
    );

}
