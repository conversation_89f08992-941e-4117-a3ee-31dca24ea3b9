<?php

class Qdlib_Const_MaterialMap
{
    // 素材类型
    const CATE_KEY_1 = '题目组合类';
    const CATE_KEY_2 = '纯名师类';
    const CATE_KEY_3 = '名师组合类';
    const CATE_KEY_4 = '剧情创新类';
    const CATE_KEY_5 = '教材组合类';
    const CATE_KEY_6 = '礼盒类';
    const CATE_KEY_7 = '品牌类';
    const CATE_KEY_8 = '其他类';
    const CATE_KEY_9 = '资料类';
    const CATE_KEY_10 = '内容类';
    const CATE_KEY_11 = '教材类';
    const CATE_KEY_12 = '教材组合';
    const CATE_KEY_13 = '组合类';
    const CATE_KEY_14 = '口播组合类';
    const CATE_KEY_15 = '情景剧组合类';
    const CATE_KEY_16 = '礼盒组合类';
    const CATE_KEY_17 = '品牌组合类';
    const CATE_KEY_18 = '课程类';
    //2023.6.14 add
    const CATE_KEY_19 = '素人纯口播类';
    const CATE_KEY_20 = '素人口播组合类';
    const CATE_KEY_21 = '名师纯口播类';
    const CATE_KEY_22 = '情景剧类';
    const CATE_KEY_23 = '达人类';
    const CATE_KEY_24 = '达人组合类';

    const CATE_MAP = [
        self::CATE_KEY_14,
        self::CATE_KEY_15,
        self::CATE_KEY_2,
        self::CATE_KEY_3,
        self::CATE_KEY_16,
        self::CATE_KEY_17,
        self::CATE_KEY_18,
        self::CATE_KEY_8,
        self::CATE_KEY_1,
        self::CATE_KEY_4,
        self::CATE_KEY_5,
        self::CATE_KEY_6,
        self::CATE_KEY_7,
        self::CATE_KEY_9,
    ];

    const GDT_CATE_MAP = [
        '情' => self::CATE_KEY_15,
        '口' => self::CATE_KEY_14,
        '纯' => self::CATE_KEY_2,
        '名' => self::CATE_KEY_3,
        '礼' => self::CATE_KEY_16,
        '品' => self::CATE_KEY_17,
        '课' => self::CATE_KEY_18,
        '题' => self::CATE_KEY_1,
        '剧' => self::CATE_KEY_4,
        '教' => self::CATE_KEY_5,
        '内' => self::CATE_KEY_10,
        '教2' => self::CATE_KEY_12,
        '资' => self::CATE_KEY_9,
        '组' => self::CATE_KEY_13,
        '其' => self::CATE_KEY_8,
    ];

    const PYQ_CATE_MAP = self::GDT_CATE_MAP;

    // 素材来源
    const SOURCE_MAP = [
        'KG' => 'KG',
        'EJ' => 'EJ',
        'DL' => 'DL',
        'TP' => 'TP',
    ];

    // 文件类型
    const FILE_TYPE_IMAGE = 'image';
    const FILE_TYPE_VIDEO = 'video';
    const FILE_TYPE_MAP = [
        self::FILE_TYPE_IMAGE => '图片',
        self::FILE_TYPE_VIDEO => '视频',
    ];
}