<?php
/***************************************************************************
 *
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file Tools.php
 * <AUTHOR>
 * @date 2013-12-18
 * @brief 通用工具
 **/
class Hk_Util_Tools {

    const TEST_TAG      = 'rdqa';
    const AUTO_TEST_TAG = 'zats';

    public static $nmqskip = null;

    public static function isTestRequest() {
        if (Hk_Util_Ip::isInnerIp()) {          // 内网调试
            if (trim($_COOKIE['APP_DEBUG']) == strval(date('Y').(date('n') - 1))
                    || trim($_GET['skip']) == self::TEST_TAG || trim($_POST['skip']) == self::TEST_TAG || trim(self::$nmqskip == self::TEST_TAG)) {
                Bd_Log::addNotice("rdqa_test_request", 1);
                return true;
            }
        }
        return false;
    }

    /**
     * 内网自动化测试判断
     *
     * @return boolean
     */
    public static function isAutoTest() {
        if (Hk_Util_Ip::isInnerIp()) {
            $autoFlag = trim(strval($_REQUEST['__auto__']));
            if ($autoFlag == self::AUTO_TEST_TAG) {
                Bd_Log::addNotice("auto_test_req", 1);
                return true;
            }
        }
        return false;
    }

    /**
     * 用于线上回归测试跳过验证的假账号
     *
     * @param string      $phone
     * @return boolean
     */
    public static function checkWhitelist($phone) {
        if (Hk_Util_Ip::isInnerIp()) {
            return ($phone >="11500800001" && $phone <= "11501000000") ? true : false;
        }
        return false;
    }

    /**
     * 获取Spam的附加信息(提交检查用)
     */
    public static function getSpamRequest() {
        $result = Saf_SmartMain::getCgi();
        $result['user']   = Saf_SmartMain::getUserInfo();
        $result['cookie'] = $_COOKIE;
        $result['client'] = array (
            'terminal'    => Hk_Util_Client::getTerminal(),
            'from_id'     => Hk_Util_Client::getFromId(),
            'submit_from' => Hk_Util_Client::getSubmitFrom(),
            );
        return $result;
    }

    public static function openSign($qid, $title, $content) {
        $qid = $qid == NULL?'':$qid;
        $title = $title == NULL? '':$title;
        $content = $content == NULL? '' :$content;
        $openConf = Bd_Conf::getConf('/cms/napi/open/submit');
        $security_key = $openConf['security_key'];
        $appkey = $openConf['appkey'];
        $str = "$security_key&$appkey&$qid&$title&$content";
        return md5($str);
    }

    /**
     * 校验手机号是否合法
     *
     * @param string        $phone
     * @return boolean
     */
    public static function checkPhoneAvalilable($phone) {
        if (!is_numeric($phone) || 11 !== strlen($phone)) {
            return false;
        }
        $phonePreSet = array(           # 运营商手机号开头规则集合
            # 中国移动
            '134' => 1, '135' => 1, '136' => 1, '137' => 1, '138' => 1, '139' => 1, '147' => 1, '148' => 1, '150' => 1,
            '151' => 1, '152' => 1, '157' => 1, '158' => 1, '159' => 1, '178' => 1, '182' => 1, '183' => 1, '184' => 1,
            '187' => 1, '188' => 1, '198' => 1, '165' => 1, '172' => 1, '195' => 1, '197' => 1,
            # 中国联通
            '130' => 1, '131' => 1, '132' => 1, '145' => 1, '146' => 1, '155' => 1, '156' => 1, '166' => 1, '167' => 1,
            '175' => 1, '176' => 1, '185' => 1, '186' => 1, '169' => 1, '196' => 1,
            # 中国电信
            '133' => 1, '141' => 1, '153' => 1, '162' => 1, '173' => 1, '174' => 1, '177' => 1, '180' => 1, '181' => 1,
            '189' => 1, '191' => 1, '199' => 1, '190' => 1, '192' => 1, '193' => 1,
            # 虚拟运营商
            '170' => 1, '171' => 1,
            # 直播课主讲账号
            '116' => 1, '115' => 1, '111' => 1,
        );
        $prefix = substr($phone, 0, 3);
        return isset($phonePreSet[$prefix]) ? true : false;
    }

    /**
     * 昵称转换，防止隐私暴露
     *
     * @param string      $uname
     * @return string
     */
    public static function unameConvert($uname) {
        return is_numeric($uname) && 11 === strlen($uname) ? substr($uname, 0, 3) . "****" . substr($uname, 7, 4) : $uname;
    }

    /**
     * 校验手机号码是否符合规范
     * @param  int    $phone 手机号码
     * @return bool   true/false
     */
    public static function checkPhoneFormat($phone) {
        return self::checkPhoneAvalilable($phone);
    }

    /**
     * 检测访问来源移动端
     */
    public static function isMobile() {
        // 如果有HTTP_X_WAP_PROFILE则一定是移动设备
        if (isset ($_SERVER['HTTP_X_WAP_PROFILE']))
        {
            return true;
        }
        // 如果via信息含有wap则一定是移动设备,部分服务商会屏蔽该信息
        if (isset ($_SERVER['HTTP_VIA']))
        {
            // 找不到为flase,否则为true
            return stristr($_SERVER['HTTP_VIA'], "wap") ? true : false;
        }
        // 脑残法，判断手机发送的客户端标志,兼容性有待提高
        if (isset ($_SERVER['HTTP_USER_AGENT']))
        {
            $clientkeywords = array ('nokia',
                'sony',
                'ericsson',
                'mot',
                'samsung',
                'htc',
                'sgh',
                'lg',
                'sharp',
                'sie-',
                'philips',
                'panasonic',
                'alcatel',
                'lenovo',
                'iphone',
                'ipod',
                'ipad',
                'blackberry',
                'meizu',
                'android',
                'netfront',
                'symbian',
                'ucweb',
                'windowsce',
                'palm',
                'operamini',
                'operamobi',
                'openwave',
                'nexusone',
                'cldc',
                'midp',
                'wap',
                'mobile'
            );

            // 从HTTP_USER_AGENT中查找手机浏览器的关键字
            if (preg_match("/(" . implode('|', $clientkeywords) . ")/i", strtolower($_SERVER['HTTP_USER_AGENT'])))
            {
                return true;
            }
        }
        // 协议法，因为有可能不准确，放到最后判断
        if (isset ($_SERVER['HTTP_ACCEPT']))
        {
            // 如果只支持wml并且不支持html那一定是移动设备
            // 如果支持wml和html但是wml在html之前则是移动设备
            if ((strpos($_SERVER['HTTP_ACCEPT'], 'vnd.wap.wml') !== false) && (strpos($_SERVER['HTTP_ACCEPT'], 'text/html') === false || (strpos($_SERVER['HTTP_ACCEPT'], 'vnd.wap.wml') < strpos($_SERVER['HTTP_ACCEPT'], 'text/html'))))
            {
                return true;
            }
        }
        return false;
    }

    /**
     * 检测邮箱的合法性
     */
    public static function checkEmailValid($email='') {
        if (empty($email)) {
            return false;
        }
        $email = filter_var($email, FILTER_VALIDATE_EMAIL);
        if (!$email) {
            return false;
        }
        return true;
    }

    public static function getbin2hexString($str='') {
        if (empty($str)) {
            return $str;
        }
        $str = "unhex('".bin2hex($str)."')";
        return $str;
    }

    /**
     * 生成随机用户名
     * @return string
     */
    public static function getRandomUname() {
        $char = chr(rand(65, 90)) . chr(rand(65, 90));
        $num  = sprintf('%03d', rand(0, 999));
        return '大栗' . $char . $num;
    }
}
