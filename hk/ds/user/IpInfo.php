<?php
/***************************************************************************
 * 
 * Copyright (c) 2015 Baidu.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file IpInfo.php
 * <AUTHOR>
 * @date 2015/02/06 23:44:50
 * @brief 
 *  
 **/

class Hk_Ds_User_IpInfo
{

    private $_objDaoIpInfo;
    private static $fields = array(
                                 'id',
                                 'ipStart',
                                 'ipEnd',
                                 'extpack',
                             ); 
    private static $ipInfo = array();
    public function __construct()
    {
        $this->_objDaoIpInfo = new Hk_Dao_User_IpInfo();
    }
   
    public function getInfoByIp( $intIp )
    {
        if( empty($intIp) )
        {
           return false;
        }
        $conds[] = 'ipStart <= '.$intIp;
        $appends[] = 'ORDER BY ipStart DESC';
        $appends[] = 'LIMIT 1';
        $re = $this->_objDaoIpInfo->getListByConds( $conds , self::$fields , NULL , $appends );
        return $re;
    } 

    public function getExtInfoByIp( $intIp, $boolCache = true )
    {
        return $this->getExtInfoByIpNew(long2ip($intIp), $boolCache);
    }

    /**
     * 通过ip获取信息，支持v6
     * @param  string    $strIp
     * @param bool $boolCache
     * @return array|bool
     */
    public function getExtInfoByIpNew( $strIp, $boolCache = true )
    {
        if (empty($strIp)) {
            return false;
        }
        if (isset(self::$ipInfo[$strIp])) {
            return self::$ipInfo[$strIp];
        }

        $ipInfo = array();
        $key      = 'common_ipinfo_' . $strIp;
        //$objCache = Hk_Service_Memcached::getInstance('common');
        $objCache = Hk_Service_RedisClient::getInstance("common");
        if ($boolCache) {
            $ipInfo = $objCache->get($key);
            if(!empty($ipInfo)) {
                $ipInfo = json_decode($ipInfo, true);
            }
        }
        if (!$boolCache || !$ipInfo) {
            $input = [
                'ip'=>strval($strIp),
            ];
            $ret = Hk_Service_Rpc::call('iploc', 'getLocation', $input);
            if (!isset($ret['errno']) || $ret['errno'] !== 0) {
                return false;
            }
            $ipInfo = array(
                'isp'         => $ret['data']['isp'],
                'country'     => $ret['data']['country'],
                'city'        => $ret['data']['city'],
                'province'    => $ret['data']['province'],
                'countryCode' => '',
                'county'      => '',
                'area'        => '',
            );
            $objCache->set($key, json_encode($ipInfo), 86400);
        }
        self::$ipInfo[$strIp] = $ipInfo;
        return $ipInfo;
    }
}
