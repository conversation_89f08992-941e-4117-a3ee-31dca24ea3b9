<?php

/**
 * @file Exam.php
 * <AUTHOR>
 * @date 2017-06-06
 * @version $Revision$-
 * @brief 试卷相关
 *
 * @deprecated  已废弃 2020/06/18
 *
 *
 **/

class Hk_Ds_Practice_Exam {

    private $_objDaoExamCategory;
    private $_objDaoExamDetail;
    private $_objDaoExamStatis;
    private $_objDaoExamPointStatis;

    public function __construct() {
        $this->_objDaoExamCategory    = new Hk_Dao_Practice_ExamCategory();
        $this->_objDaoExamDetail      = new Hk_Dao_Practice_ExamDetail();
        $this->_objDaoExamStatis      = new Hk_Dao_Practice_ExamStatis();
        $this->_objDaoExamPointStatis = new Hk_Dao_Practice_ExamPointStatis();
    }


    //>>>>>>>>>>>>试卷出卷及详情>>>>>>>>>>
    /**
     * 获取 省 or 直辖市-市 的examId
     * 会把所有孩子的试卷全部取出来
     * @param array   $termInfo array(courseId,gradeId,semesterId,typeId)
     * @param array   $lbsInfo  array(provinceId,cityId,areaId)
     * @return null-参数参数,false-交互失败,array(eid1,eid2,...)
     */
    public function getExamidByProvince($termInfo, $lbsInfo) {
        if ( (0 >= $termInfo['courseId']) || (0 >= $termInfo['gradeId']) || (0 >= $termInfo['typeId']) || (0 >= $lbsInfo['provinceId']) ) {
            return null;
        }
        if ( (0 != $termInfo['semesterId']) && (1 != $termInfo['semesterId']) ) {
            return null;
        }

        $path = Practice_Tools::parseLocLv1Path($lbsInfo);
        $cnt  = count($path);
        if (!$cnt || (1 > $cnt)) {
            return null;
        }

        $data = array();
        $term = "{$termInfo['courseId']}_{$termInfo['gradeId']}_{$termInfo['semesterId']}"; 
        if (1 == $cnt) {
            $ret = $this->_objDaoExamCategory->getLbsExamid($term, $termInfo['typeId'], $path[0]);
        } elseif (2 == $cnt) {
            $ret = $this->_objDaoExamCategory->getLbsExamid($term, $termInfo['typeId'], $path[0], $path[1]);
        }
        if (empty($ret)) {
            return $ret;
        }

        foreach ($ret as $key => $val) {
            $eid = intval($val['examId']);
            $data[$eid] = $eid;
        }

        return $data;
    }


    /**
     * 获取 省-市 or 直辖市-市-区 的examId
     * @param array   $termInfo array(courseId,gradeId,semesterId,typeId)
     * @param array   $lbsInfo  array(provinceId,cityId,areaId)
     * @return null-参数参数,false-交互失败,array(eid1,eid2,...)
     */
    public function getExamidByCity($termInfo, $lbsInfo) {
        if ( (0 >= $termInfo['courseId']) || (0 >= $termInfo['gradeId']) || (0 >= $termInfo['typeId']) || (0 >= $lbsInfo['provinceId']) || (0 >= $lbsInfo['cityId']) ) {
            return null;
        }
        if ( (0 != $termInfo['semesterId']) && (1 != $termInfo['semesterId']) ) {
            return null;
        }

        $path = Practice_Tools::parseLocLv2Path($lbsInfo);
        $cnt  = count($path);
        if (2 > $cnt) {
            return null;
        }

        $data = array();
        $term = "{$termInfo['courseId']}_{$termInfo['gradeId']}_{$termInfo['semesterId']}"; 
        if (2 == $cnt) {
            $ret = $this->_objDaoExamCategory->getLbsExamid($term, $termInfo['typeId'], $path[0], $path[1]);
        } elseif (3 == $cnt) {
            $ret = $this->_objDaoExamCategory->getLbsExamid($term, $termInfo['typeId'], $path[0], $path[1], $path[2]);
        }
        if (empty($ret)) {
            return $ret;
        }

        foreach ($ret as $key => $val) {
            $eid = intval($val['examId']);
            $data[$eid] = $eid;
        }

        return $data;
    }

    
    /**
     * 获取限定学科+年级+学期的指定版本的默认examId
     * @param array   $termInfo array(courseId,gradeId,semesterId,typeId)
     * @param array   $vers     array(26,23,65[,...])
     * @return null-参数参数,false-交互失败,array("26"=>array(eid1,eid2[,...]) [,"23"=>array(eid1,...)])
     */
    public function getDefaultExamidByVers($termInfo, $vers=array()) {
        if ( (0 >= $termInfo['courseId']) || (0 >= $termInfo['gradeId']) || (0 >= $termInfo['typeId']) || !$vers || !is_array($vers) ) {
            return null;
        }
        if ( (0 != $termInfo['semesterId']) && (1 != $termInfo['semesterId']) ) {
            return null;
        }

        $term = "{$termInfo['courseId']}_{$termInfo['gradeId']}_{$termInfo['semesterId']}";
        $ret  = $this->_objDaoExamCategory->getDefaultExamidByVers($term, $termInfo['typeId'], $vers);
        return $ret;
    }


    /**
     * 获取试卷详情
     * @param integer   $examId 
     * @return false-交互失败,array
     */
    public function getExamDetailByExamId($examId) {
        if (0 >= $examId) {
            return false;
        }

        $ret = $this->_objDaoExamDetail->getByExamId($examId);
        if (!empty($ret)) {
            foreach ($ret as $r_key => &$r_val) {
                if (isset($r_val['content'])) {
                    $r_val['content'] = json_decode($r_val['content'], true);
                }
                if (isset($r_val['tidList'])) {
                    $r_val['tidList'] = json_decode($r_val['tidList'], true);
                }
            }
            unset($r_val);
            $ret = $ret[0];
        }

        return $ret;
    }

    /**
     * 获取试卷详情 不区分直播和平台
     * @param integer   $examId
     * @return false-交互失败,array
     */
    public function getAllExamDetailByExamId($examId) {
        if (0 >= $examId) {
            return false;
        }

        $ret = $this->_objDaoExamDetail->getAllByExamId($examId);
        if (!empty($ret)) {
            foreach ($ret as $r_key => &$r_val) {
                if (isset($r_val['content'])) {
                    $r_val['content'] = json_decode($r_val['content'], true);
                }
                if (isset($r_val['tidList'])) {
                    $r_val['tidList'] = json_decode($r_val['tidList'], true);
                }
            }
            unset($r_val);
            $ret = $ret[0];
        }

        return $ret;
    }

    /*
     * 获取多个试卷详情
     * @param array $examIds
     * $return array|bool
     */
    public function getExamDetailByExamIds($examIds, $mark = 0) {
        if (empty($examIds)) {
            return false;
        }

        $ret = $this->_objDaoExamDetail->getByExamId($examIds, $mark);
        if (!empty($ret)) {
            foreach ($ret as $r_key => &$r_val) {
                if (isset($r_val['content'])) {
                    $r_val['content'] = json_decode($r_val['content'], true);
                }
                if (isset($r_val['tidList'])) {
                    $r_val['tidList'] = json_decode($r_val['tidList'], true);
                }
            }
            unset($r_val);
        }
        return $ret;
    }


    //<<<<<<<<<<<试卷出卷及详情<<<<<<<<<<<






    //>>>>>>>>>>>>试卷的统计>>>>>>>>>>
    /**
     * 获取试卷的统计数据
     * @param integer $examId 
     * @return null-参数参数,false-交互失败,array()
     */
    public function getExamStatisByExamId($examId) {
        if (0 >= $examId) {
            return null;
        }

        $ret = $this->_objDaoExamStatis->getByExamId($examId);
        if (!empty($ret)) {
            foreach ($ret as $r_key => &$r_val) {
                if (isset($r_val['ext'])) {
                    $r_val['ext'] = json_decode($r_val['ext'], true);
                }
                if (isset($r_val['graph'])) {
                    $r_val['graph'] = json_decode($r_val['graph'], true);
                }
                if (isset($r_val['userScores'])) {  #"uid1":score1,"uid2":score2..."uidN":scoreN,
                    $temp = "{".substr($r_val['userScores'], 0, -1)."}";
                    $r_val['userScores'] = @json_decode($temp, true);
                }

            }
            unset($r_val);
            $ret = $ret[0];
        }

        return $ret;
    }


    /**
     * 增加试卷维度的累计分
     * @param array $paras  array(array('examId','score'), array(...) ) 
     * @return null-参数参数,false-交互失败,array()
     */
    public function incrExamStatisForScore($paras) {
        if ( !is_array($paras) || empty($paras) ) {
            return null;
        }
        foreach ($paras as $key => $val) {
            if ( (0 >= $val['examId']) || !isset($val['score'])) {
                return null;
            }
        }

        $ret = $this->_objDaoExamStatis->incrForScore($paras);

        return $ret;
    }
    //<<<<<<<<<<<试卷的统计<<<<<<<<<<<
    




    //>>>>>>>>>>>>试卷下知识点的统计>>>>>>>>>>
    /**
     * 获取试卷-知识点维度的数据
     * @param integer $examId 
     * @param array   $pids     array(26,23,65[,...])
     * @return null-参数参数,false-交互失败,array()
     */
    public function getExamPointStatisByPoints($examId, $pids) {
        if ( (0 >= $examId) || !is_array($pids) || empty($pids) ) {
            return null;
        }

        $ret = $this->_objDaoExamPointStatis->getByExamPoints($examId, $pids);
        if (!empty($ret)) {
            foreach ($ret as $r_key => &$r_val) {
                if (isset($r_val['ext'])) {
                    $r_val['ext'] = json_decode($r_val['ext'], true);
                }
            }
            unset($r_val);
        }

        return $ret;
    }


    /**
     * 增加试卷-知识点维度的累计分
     * @param array $paras  array(array('examId','pointId','score',['total']), array(...) )  total非必需 
     * @return null-参数参数,false-交互失败,array()
     */
    public function incrExamPointStatisForScore($paras) {
        if ( !is_array($paras) || empty($paras) ) {
            return null;
        }
        foreach ($paras as $m_key => &$m_val) {
            if ( (0 >= $m_val['examId']) || (0 > $m_val['pointId']) || !isset($m_val['score']) ) {
                return null;
            }
            if ( isset($m_val['total']) && (0 >= $m_val['total']) ) {
                unset($m_val['total']);
            }
        }
        unset($m_val);

        $ret = $this->_objDaoExamPointStatis->incrForScore($paras);

        return $ret;
    }

    /**
     * 返回统计数量
     * @param int   $courseId
     * @param int   $typeId
     * @param int   $gradeId
     * @param int   $semesterId
     * @return false|int
     */
    public function getCntByCourse($courseId, $typeId, $gradeId = 0, $semesterId = 0) {
        if (empty($courseId)) {
            return 0;
        }
        $conds = array(
            'courseId' => $courseId,
            'typeId'   => $typeId,
        );
        if (5 === $typeId) {
            if ($semesterId > 0) {
                $conds['gradeId']    = $gradeId;
            }
            if ($semesterId > 0) {
                $conds['semesterId'] = $semesterId;
            }
        }

        $ret = $this->_objDaoExamDetail->getCntByCourse($conds);

        return (false === $ret) ? 0 : $ret;
    }

    /**
     * ral调用检索服务
     * @param array $arrQueryParams
     * @param int $intPn
     * @param int $intRn
     */
    public function getListByQueryParams($arrQueryParams, $intPn = 0,$intRn = 10) {
        $intPgnum = intval($intPn/$intRn);

        $strLogQuery = ''; //打点使用
        foreach($arrQueryParams as $strParamKey => &$strParamValue){
            if($strParamKey === 'filters'){
                $strParamValue = json_encode($strParamValue, JSON_UNESCAPED_UNICODE);
                $strLogQuery .= sprintf('[%s : %s] ', $strParamKey, $strParamValue);
            }
            else{
                $strParamValue = Hk_Util_String::utf8_to_gbk($strParamValue);
                $strLogQuery .= sprintf('[%s : %s] ', $strParamKey, Hk_Util_String::gbk_to_utf8($strParamValue));
            }
        }
        //参数只有query和filters
        $arrReq = array(
            'query'        => strval($arrQueryParams['query']),
            'filters'      => $arrQueryParams['filters'], //filters中ProvinceId,默认为0,例:0为搜索全国,1为搜索北京
            'page_no'      => intval($intPgnum),
            'num_per_page' => intval($intRn)+1, //多取一个用来判断hasMore
            'searchid'     => sprintf('%s%s', time(), rand(100, 999)),
            'query_type'   => 'query_type_all',
            'query_source' => 'normal',
            'source'       => 'mobile',
        );
        $ret    = $this->requestPost('examRcsV1',$arrReq);
        if (!empty($ret) && $ret['err_no'] === 0) {
            $arrList = $this->_format($ret, $intRn);
            return $arrList;
        } else {
            Bd_Log::warning("Fail to search from exam RCS V1. ret is empty.");
            return false;
        }
    }

    /**
     * @param $data
     * $data = array(
     *      'ret_num'       => 10,
     *      'disp_num'      => 150,
     *      'err_no'        => 0,
     *      'query_terms'   => '数学',
     *      'result1'       => array(),
     *      ...
     *      'resultN'       => array(
     *          'title'            => '$九年级$数学$$$2013 168185326985625',
     *          'displayOnline'    => '{"result":{"content":"","id":"5292","qualityLevel":"","site":"","summary":"","table":"","title":"$九年级$数学$$$2013 168185326985625","wordcntInt":""}}',
     *          'URL'              => 'www.zybang.com/question/5292.html',
     *          'category'         => 'ZUOWEN',
     *          'pubtime'          => '1479128409',
     *          'subcate'          => 'tablegradeid4courseid2typeid5semesterid0provinceid0',
     *          'weight'           => 6400,
     *          'bweight'          => 64,
     *          'modelType'        => 0,
     *          'listType'         => 4,
     *          'serviceType'      => 2,
     *          'requirement_type' => 0,
     *      ),
     * )
     */
    private function _format($data, $rn) {
        $return = array(
            'errNo'   => $data['err_no'],
            'allNum'  => $data['disp_num'],
            'retNum'  => $data['ret_num'],
            'query'   => Hk_Util_String::gbk_to_utf8($data['query_terms']),
            'examIds' => array(),
            'hasMore' => 0,
        );
        unset($data['err_no']);
        unset($data['disp_num']);
        unset($data['ret_num']);
        unset($data['query_terms']);
        //判断hasMore
        $hasMore = ($rn < count($data)) ? 1 : 0;
        if ($hasMore) {
            //过滤掉多的一条
            array_pop($data);
        }
        $return['hasMore'] = $hasMore;
        //处理examIds
        $examIds = array();
        foreach ($data as $key => $val) {
            $str       = Hk_Util_String::gbk_to_utf8($val['displayOnline']);
            $temp      = @json_decode($str, true);
            $examIds[] = intval($temp['result']['id']);
        }
        $return['examIds'] = $examIds;

        return $return;
    }

    /*
     * ral调用
     */
    public function requestPost($model,$arrParam){
        $packReq    = mc_pack_array2pack($arrParam,PHP_MC_PACK_V2);
        $intPackLen = strlen($packReq);

        $arrHead    = array(
            'log_id'   => 111,
            'provider' => 'microsite-debug',
            'body_len' => $intPackLen,      //必需要填写body_len
        );

        $arrRes = ral($model, 'post', $arrParam, '',$arrHead);
        if ($arrRes === false) {
            return false;
        }
        return $arrRes;
    }
    //<<<<<<<<<<<试卷下知识点的统计<<<<<<<<<<<
}
