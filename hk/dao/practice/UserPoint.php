<?php

/**
 * @file UserPoint.php
 * <AUTHOR>
 * @date 2017-06-07
 * @version $Revision$-
 * @brief 用户-知识点练习记录
 *
 * @deprecated  已废弃 2020/06/18
 *
 **/

class Hk_Dao_Practice_UserPoint extends Hk_Common_BaseDao {
    //用户分表
    const TABLE_NUM = 20;

    /**
     * 待连接的数据表名称前缀
     * @var string
     */
    protected $_tablePrefix;

    public function __construct() {
        $this->_dbName      = "zybpractice/practice";
        $this->_table       = "tblUserPoint";
        $this->_tablePrefix = "tblUserPoint";
        $this->arrFieldsMap = array(
            'id'             => 'id',
            'uid'            => 'uid',
            'cuid'           => 'cuid',
            'pointId'        => 'pointId',
            'mtime'          => 'mtime',
            'degree'         => 'degree',
            'lastDegree'     => 'lastDegree',
            'doneList'       => 'doneList',
            'lastList'       => 'lastList',
            'bit'            => 'bit',
            'ext'            => 'ext',
        );

        $this->arrTypesMap = array(
            'id'             => Hk_Service_Db::TYPE_INT,    
            'uid'            => Hk_Service_Db::TYPE_INT,    
            'cuid'           => Hk_Service_Db::TYPE_STR,    
            'pointId'        => Hk_Service_Db::TYPE_INT,     
            'mtime'          => Hk_Service_Db::TYPE_INT,    
            'degree'         => Hk_Service_Db::TYPE_INT,    
            'lastDegree'     => Hk_Service_Db::TYPE_INT,    
            'doneList'       => Hk_Service_Db::TYPE_JSON, //{[pointId:["c":choice_bit,"j":1/0_对/错]]}
            'lastList'       => Hk_Service_Db::TYPE_JSON, //{[pointId:["c":choice_bit,"j":1/0_对/错]]}
            'bit'            => Hk_Service_Db::TYPE_INT,      
            'ext'            => Hk_Service_Db::TYPE_JSON,    
        );
    }


    /**
     * 查询用户对知识点练习的记录
     * @param  int    $uid
     * @param  string $cuid
     * @param  array  $pids   
     * @param  array  $arrFields  需要查询的字段名数组，格式必须为数组
     * @return mix
     */
    public function getByPointids($uid, $cuid='', $pids, $arrFields=array(), $limit=200) {

        //确定分表    
        $result = $this->setPartionTable($uid, $cuid);
        if (!$result) {
            return false;
        }

        if(empty($arrFields)) {
            $arrFields = array('id', 'uid', 'cuid', 'pointId', 'mtime', 'degree', 'lastDegree', 'doneList', 'lastList', 'bit', 'ext');
        }
        //查询字段的转换
        $arrFields = Hk_Service_Db::mapField($arrFields, $this->arrFieldsMap, true);
        $strFields = implode(",", $arrFields);

        $strPids = array();
        foreach ($pids as $key => $val) {
            $strPids[] = intval($val);
        }
        $strPids = implode(",", $strPids);

        //处理uid和cuid的关系
        if (0 < $uid) {
            $sql = sprintf("SELECT %s FROM %s WHERE uid=%d AND pointId IN(%s) LIMIT %d", 
                $strFields,
                $this->_table,
                intval($uid),
                $strPids,
                intval($limit)
            );
        } else {
            $sql = sprintf("SELECT %s FROM %s FORCE INDEX(s_uid_pid_cuid) WHERE uid=0 AND cuid=unhex('%s') AND pointId IN(%s) LIMIT %d", 
                $strFields,
                $this->_table,
                bin2hex($cuid),
                $strPids,
                intval($limit)
            );
        }
        
        $data = $this->query($sql);
        if (false === $data) {
            Bd_Log::warning("Error[dbError] Detail[db select failed, sql:{$sql}");
            return false;
        }
        return $data;
    }


    /**
     * Select查询，根据user获取列表
     * @param  int    $uid
     * @param  string $cuid
     * @param  int    $pn
     * @param  int    $rn 
     * @param  array  $arrFields  需要查询的字段名数组，格式必须为数组
     * @return mix
     */
    public function getListByUser($uid, $cuid, $pn=0, $rn=200, $arrFields=array()) {

        //确定分表    
        $result = $this->setPartionTable($uid, $cuid);
        if (!$result) {
            return false;
        }

        if(empty($arrFields)) {
            $arrFields = array('id', 'uid', 'cuid', 'pointId', 'mtime', 'degree', 'lastDegree', 'doneList', 'lastList', 'bit', 'ext');
        }
        //查询字段的转换
        $strFields = array();
        foreach ($arrFields as $key => $val) {
            if (isset($this->arrFieldsMap[$val])) {
                //!!!注意 id 字段的特殊处理!!!
                $rowName = ('id' == $val)? "b.".$val : $val;
                $strFields[] = $rowName;
            }
        }
        $strFields = implode(",", $strFields);

        //处理uid和cuid的关系
        if (0 < $uid) {
            $sql = sprintf("SELECT %s FROM (SELECT id FROM %s WHERE uid=%d LIMIT %d, %d) a, %s b WHERE a.id=b.id", 
                $strFields,
                $this->_table,
                intval($uid),
                intval($pn),
                intval($rn),
                $this->_table
            );
        } else {
            $sql = sprintf("SELECT %s FROM (SELECT id FROM %s WHERE uid=0 AND cuid=unhex('%s') LIMIT %d, %d) a, %s b WHERE a.id=b.id", 
                $strFields,
                $this->_table,
                bin2hex($cuid),
                intval($pn),
                intval($rn),
                $this->_table
            );
        }
        
        $data = $this->query($sql);
        if (false === $data) {
            Bd_Log::warning("Error[dbError] Detail[db select failed, sql:{$sql}]");
            return false;
        }
        return $data;
    }


    /**
     * 增加记录
     * @param  int    $uid
     * @param  string $cuid
     * @param  array  $paras   
     * @return mix
     */
    public function add($uid, $cuid, $paras) {

        //确定分表 
        $result = $this->setPartionTable($uid, $cuid);
        if (!$result) {
            return false;
        }

        $sql = sprintf("INSERT INTO %s(uid,cuid,pointId,mtime,degree,lastDegree,doneList,lastList,bit,ext) VALUES(%d,unhex('%s'),%d,%d,%d,%d,unhex('%s'),unhex('%s'),%d,unhex('%s'))", 
            $this->_table,
            intval($uid),
            bin2hex($cuid),
            intval($paras['pointId']),
            time(),
            intval($paras['degree']),
            intval($paras['lastDegree']),
            isset($paras['doneList'])? bin2hex($paras['doneList']) : bin2hex("[]"),
            isset($paras['lastList'])? bin2hex($paras['lastList']) : bin2hex("[]"),
            intval($paras['bit']),
            isset($paras['ext'])? bin2hex($paras['ext']) : bin2hex("[]")
        );

        $data = $this->query($sql);
        if (false === $data) {
            Bd_Log::warning("Error[dbError] Detail[db insert failed, sql:{$sql}");
            return false;
        }

        return $data;
    }


    /**
     * 增加记录（为单个用户导数据使用）
     * @param  int    $uid
     * @param  string $cuid
     * @param  array  $items   
     * @return mix
     */
    public function addBatch($uid, $cuid, $items) {
        if ( !is_array($items) || empty($items)) {
            return false;
        }

        //确定分表 
        $result = $this->setPartionTable($uid, $cuid);
        if (!$result) {
            return false;
        }

        $tpl    = "(%d,unhex('%s'),%d,%d,%d,%d,unhex('%s'),unhex('%s'),%d,unhex('%s'))";
        $values = array();
        foreach ($items as $key => $val) {
            $values[] = sprintf(
                $tpl,
                intval($uid), 
                bin2hex($cuid), 
                intval($val['pointId']), 
                intval($val['mtime']), 
                intval($val['degree']), 
                intval($val['lastDegree']), 
                isset($val['doneList'])? bin2hex($val['doneList']) : bin2hex("[]"),
                isset($val['lastList'])? bin2hex($val['lastList']) : bin2hex("[]"),
                intval($val['bit']), 
                isset($val['ext'])? bin2hex($val['ext']) : bin2hex("[]")
            );
        }

        $sql = sprintf("INSERT INTO %s(uid,cuid,pointId,mtime,degree,lastDegree,doneList,lastList,bit,ext) VALUES%s", 
            $this->_table,
            implode(",", $values)
        );

        $data = $this->query($sql);
        if (false === $data) {
            Bd_Log::warning("Error[dbError] Detail[db insert failed, sql:{$sql}]");
            return false;
        }

        return $data;
    }


    /**
     * 批量删除记录，按pids（为单个用户使用）
     * @param  int    $uid
     * @param  string $cuid
     * @param  array  $pids   
     * @return mix
     */
    public function delByUserPids($uid, $cuid, $pids) {
        if ( !is_array($pids) || empty($pids)) {
            return false;
        }

        //确定分表    
        $result = $this->setPartionTable($uid, $cuid);
        if (!$result) {
            return false;
        }

        $strPids = array();
        foreach ($pids as $key => $val) {
            $strPids[] = intval($val);
        }
        $strPids = implode(",", $strPids);

        //处理uid和cuid的关系
        if (0 < $uid) {
            $sql = sprintf("DELETE FROM %s WHERE uid=%d AND pointId IN(%s) LIMIT 200", 
                $this->_table,
                intval($uid),
                $strPids
            );
        } else {
            $sql = sprintf("DELETE FROM %s WHERE uid=0 AND cuid=unhex('%s') AND pointId IN(%s) LIMIT 200", 
                $this->_table,
                bin2hex($cuid),
                $strPids
            );
        }
        
        $data = $this->query($sql);
        if (false === $data) {
            Bd_Log::warning("Error[dbError] Detail[db select failed, sql:{$sql}]");
            return false;
        }
        return $data;
    }


    /**
     * 修改全属性
     * @param  int    $uid
     * @param  string $cuid
     * @param  array  $paras  
     * @return mix 
     */
    public function modAllByUserPoint($uid, $cuid, $pointId, $paras) {

        $result = $this->setPartionTable($uid, $cuid);
        if (!$result) {
            return false;
        }

        //处理uid和cuid的关系
        if (0 < $uid) {
            $sql = sprintf("UPDATE %s SET cuid=unhex('%s'), mtime=%d, degree=%d, lastDegree=%d, doneList=unhex('%s'), lastList=unhex('%s'), bit=%d, ext=unhex('%s') WHERE uid=%d AND pointId=%d LIMIT 30", 
                $this->_table,
                bin2hex($cuid),
                time(),
                intval($paras['degree']),
                intval($paras['lastDegree']),
                bin2hex($paras['doneList']),
                bin2hex($paras['lastList']),
                intval($paras['bit']),
                bin2hex($paras['ext']),
                intval($uid),
                intval($pointId)
            );
        } else {
            $sql = sprintf("UPDATE %s SET mtime=%d, degree=%d, lastDegree=%d, doneList=unhex('%s'), lastList=unhex('%s'), bit=%d, ext=unhex('%s') WHERE uid=0 AND cuid=unhex('%s') AND pointId=%d LIMIT 30", 
                $this->_table,
                time(),
                intval($paras['degree']),
                intval($paras['lastDegree']),
                bin2hex($paras['doneList']),
                bin2hex($paras['lastList']),
                intval($paras['bit']),
                bin2hex($paras['ext']),
                bin2hex($cuid),
                intval($pointId)
            );
        }

        $data = $this->query($sql);
        if (false === $data) {
            Bd_Log::warning("Error[dbError] Detail[db update failed, sql:{$sql}");
            return false;
        }

        return $data;
    }


    /**
     * 确定分表
     * @param int    uid
     * @param string cuid 
     * @return bool 成功返回true，否则false
     */
    protected function setPartionTable($uid = 0, $cuid = ''){
        $uid  = intval($uid);
        $cuid = trim(strval($cuid));
        if($uid <= 0 && strlen($cuid) <= 0){
            Bd_Log::warning("Error[paramError] Detail[uid:{$uid} cuid:{$cuid}]");
            return false;
        }

        if($uid > 0) {
            $tableIndex  = $uid % self::TABLE_NUM;
            $this->_table = $this->_tablePrefix. intval($tableIndex);
            $flag = "uid";
        }else{
            $tableIndex  = crc32($cuid) % self::TABLE_NUM;
            $this->_table = $this->_tablePrefix. intval($tableIndex);
            $flag = "cuid";
        }
        Bd_Log::addNotice("{$flag}_tblUserPoint", $tableIndex);
        return true;
    }
}
