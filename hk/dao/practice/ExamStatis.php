<?php

/**
 * @file ExamStatis.php
 * <AUTHOR>
 * @date 2017-06-06
 * @version $Revision$-
 * @brief 试卷维度的统计数据管理
 *
 * @deprecated  已废弃 2020/06/18
 *
 **/

class Hk_Dao_Practice_ExamStatis extends Hk_Common_BaseDao {
    public function __construct() {
        $this->_dbName      = "zybpractice/practice";
        $this->_table       = "tblExamStatis";
        $this->arrFieldsMap = array(
            'id'      => 'id',
            'examId'  => 'examId',
            'score'   => 'score',
            'num'     => 'num',
            'graph'   => 'graph',
            'ext'     => 'ext',
            'userScores' => 'userScores',
        );

        $this->arrTypesMap = array(
            'id'      => Hk_Service_Db::TYPE_INT,    
            'examId'  => Hk_Service_Db::TYPE_INT, 
            'score'   => Hk_Service_Db::TYPE_INT, 
            'num'     => Hk_Service_Db::TYPE_INT, 
            'graph'   => Hk_Service_Db::TYPE_JSON, //图形数据点json{"bottom":["s":得分,"no":排名],"top","top60p","top30p","top10p"}  
            'ext'     => Hk_Service_Db::TYPE_JSON,
            'userScores' => Hk_Service_Db::TYPE_STR,
        );
    }


    /**
     * 获取试卷维度的数据
     * @param integer $examId 
     * @return mix
     */
    public function getByExamId($examId) {
        if (0 >= $examId) {
            return false;
        }

        $arrFields = array('examId', 'score', 'num', 'graph', 'userScores');

        //查询字段的转换
        $arrFields = Hk_Service_Db::mapField($arrFields, $this->arrFieldsMap, true);
        $strFields = implode(",", $arrFields);

        $sql = sprintf("SELECT %s FROM %s WHERE examId=%d  LIMIT 10", 
            $strFields,
            $this->_table,
            intval($examId)
        );

        $data = $this->query($sql);
        if (false === $data) {
            Bd_Log::warning("Error[dbError] Detail[db select failed, sql:{$sql}");
            return false;
        }
        return $data;
    }


    /**
     * 增加试卷-知识点维度的总分
     * @param array $paras  array(array('examId','score'), array(...) )
     * @return mix
     */
    public function incrForScore($paras) {
        if ( !is_array($paras) || empty($paras) ) {
            return false;
        }

        $strValues = array();
        foreach ($paras as $key => $val) {
            if ( (0 >= $val['examId']) || !isset($val['score']) ) {
                return false;
            }
            $temp = '\"'.intval($val['uid']).'\":'.intval($val['score']).',';
            $strValues[] = sprintf("%d,%d,%d,'%s'", intval($val['examId']), intval($val['score']), 1, $temp);
        }

        $sql = sprintf("INSERT INTO %s(examId,score,num,userScores) VALUES(%s) ON DUPLICATE KEY UPDATE score=score+VALUES(score),num=num+VALUES(num),userScores=concat(userScores,VALUES(userScores))",
            $this->_table,
            implode("),(", $strValues)
        );

        $data = $this->query($sql);
        if (false === $data) {
            Bd_Log::warning("Error[dbError] Detail[db select failed, sql:{$sql}");
            return false;
        }
        return $data;
    }
}
