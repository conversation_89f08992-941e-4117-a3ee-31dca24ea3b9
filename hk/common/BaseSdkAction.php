<?php
/***************************************************************************
 *
 * Copyright (c) 2013 Baidu.com, Inc. All Rights Reserved
 *
 **************************************************************************/



/**
 * @file Base.php
 * <AUTHOR>
 * @date 2013/12/06 13:57:15
 * @brief 抽象基类
 *
 **/

abstract class Hk_Common_BaseSdkAction extends Ap_Action_Abstract {
    const CONFIG_IS_LOGIN = 'is_login';
    const SKIP_VALIDATION = 'skip_validation';
    const CONFIG_INPUT = 'input';
    const CONFIG_OUTPUT = 'output';
    const CONFIG_ICONV = 'iconv';
    const CONFIG_OUT_ICONV = 'out_iconv';
    const CONFIG_NOTNEED_UINFO = 'na_userinfo';

    protected static $CONFIG_KEYS = array(
        self::CONFIG_IS_LOGIN => '_isLogin',
        self::SKIP_VALIDATION =>'_skipValidation',
        self::CONFIG_INPUT => '_input',
        self::CONFIG_OUTPUT => '_output',
        self::CONFIG_ICONV => '_iconv',
        self::CONFIG_OUT_ICONV => '_outIconv',
        self::CONFIG_NOTNEED_UINFO => '_needNAUserInfo',
    );

    // 日志变量
    protected $logVars = array(
        'app_version' => '',
        'app_terminal' => '',
        'api_version' => '',
        'api_module' => '',
        'api_action' => '',
        'pro_errno' => '',
        'pro_un' => '',
        'pro_uid' => '',
        'pv' => '',
    );

    // 用户信息
    protected $_userInfo = array(
        'isLogin' => false,
        'uid' => 0,
        'uname' => '',
    );

    // 使用Filter过滤过的参数列表
    protected $_requestParam;

    // 配置信息项
    protected $_actionName = null;

    // controller name
    protected $_controllor = 'action';

    // api version
    protected $_apiVersion = 'v1';

    // app version 两位版本后端接口映射版本号
    protected $_appVersion = 0;

    // app versionCode 具体客户端中的版本号
    protected $_appVersionCode = 0;

    // app terminal
    protected $_appTerminal = '';

    // app type
    protected $_appType = 0;

    // app source
    protected $_appSource = 0;

    //是否跳过输入输出校验
    protected $_skipValidation = 0;

    //输出转码
    protected $_outIconv = null;

    //出入转码
    protected $_iconv = null;

    //输入验证配置
    protected $_input = null;

    //输出验证配置
    protected $_output = null;

    //是否需要用户信息
    protected $_needNAUserInfo = 0;

    // request
    protected $_request = null;

    // response
    protected $_tplData = array(
        'errNo' => 0,
        'errstr' => 'success',
        'data' => array(),
    );

	protected $_appId = '';

    /*
     * 子类特有逻辑，强制子类必须实现
     */
    abstract protected function invoke();


    //action配置文件读取,参数初始化
    protected function _init() {
        // 初始化配置文件选项
        $objRequest = $this->getRequest();
        $this->_controllor = strtolower($objRequest->getControllerName());
        $this->_actionName = strtolower($objRequest->getActionName());
        $this->_apiVersion = strtolower($objRequest->getParam('apiversion', 'v1'));
        Bd_AppEnv::setCurrAction($this->_actionName);
        Bd_AppEnv::setCurrCtl($this->_controllor);

        $configPath = implode("/", array('action', $this->_controllor, $this->_apiVersion, $this->_actionName));
        $config = Bd_Conf::getAppConf($configPath);
        if(!$config){
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::ACTION_CONF_ERROR, $configPath);
            return false;
        }

        //配置参数初始化
        foreach (self::$CONFIG_KEYS as $key => $value) {
            if (isset($config[$key])) {
                $this->$value = $config[$key];
            }
        }

        //获取用户信息
        $arrUserInfo = Saf_SmartMain::getUserInfo();
        if (intval($arrUserInfo['uid']) > 0) {
            $this->_userInfo = $arrUserInfo;
            if ($this->_needNAUserInfo) {
                $objDsUcloud = new Hk_Ds_User_Ucloud ();
                $arrUcloud = $objDsUcloud->getUserInfo ($this->_userInfo['uid']);
                if ($arrUcloud[$this->_userInfo['uid']]) {
                    $this->_userInfo['naUcloud'] = $arrUcloud;
                } else {
                    $this->_userInfo['naUcloud'] = array ();
                }
            }
        }

        //获取请求参数
        $arrRequestParam = Saf_SmartMain::getCgi();
        $this->_requestParam = !is_null($arrRequestParam['request_param'])
            ? $arrRequestParam['request_param'] : array();

        $appVersion = Hk_Util_Client::getVersion();
        $this->_appVersion = $appVersion['version'];
        $this->_appVersionCode = $appVersion['versionCode'];
        $this->_appType = $appVersion['type'];
        $this->_appSource = $appVersion['source'];
        $appTerminal = Hk_Util_Client::getTerminal();
        $this->_appTerminal = $appTerminal['terminal'];
		$this->_appId = empty($this->_requestParam["appid"]) ? 'homework':$this->_requestParam["appid"];

        Hk_Util_Log::setLog ('request_param', json_encode ($this->_requestParam));
    }

    //参数校验
    protected function _before() {

        if($this->_isLogin == 1 && $this->_userInfo['uid'] <= 0){
            throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::USER_NOT_LOGIN, '', null, Hk_Util_Exception::TRACE);
        }
        //输入校验
        if (!is_null($this->_input)) {
            try {
                if($this->_skipValidation == 0){
                    Hk_Util_Filter::setLogLevel(Hk_Util_Exception::TRACE);
                    Hk_Util_Filter::filter($this->_requestParam, $this->_input, true, false, true);
                }
            } catch (Hk_Util_Exception $e) {
                $this->_tplData['errNo'] = $e->getErrNo();
                $this->_tplData['errstr'] = $e->getErrStr();
                return false;
            }
        }

        $env = getenv("SAFE_WEB_SCAN_ENV");
        if($env=="on") {
            $stop = Hk_Util_WebScan::StopAttack();
            if($stop) {
                $this->_tplData['errNo']  = Hk_Util_ExceptionCodes::PARAM_ERROR;
                $this->_tplData['errstr'] = "输入内容存在危险字符，安全起见，已被本站拦截";
                return false;
            }
        }

        //输入转码
        if (!is_null($this->_iconv)) {
            $this->_requestParam = Bd_String::iconv_recursive( $this->_requestParam, $this->_iconv['ie'], $this->_iconv['oe']);
        }
        return true;
    }


    //输出参数校验
    protected function _after() {

        try {
            if($this->_skipValidation == 0){
                Hk_Util_Filter::setLogLevel(Hk_Util_Exception::WARNING);
                Hk_Util_Filter::filter($this->_tplData['data'], $this->_output, false);

            }
        } catch (Hk_Util_Exception $e) {
            $this->_tplData['errNo'] = $e->getErrNo();
            $this->_tplData['errstr'] = $e->getErrStr();
            $this->_tplData['data'] = array();
        }
        //输出转码
        if (!is_null($this->_outIconv)) {
            $this->_tplData = Bd_String::iconv_recursive($this->_tplData, $this->_outIconv['ie'], $this->_outIconv['oe']);
        }
    }

    protected function _display() {
        $json = json_encode ((object)$this->_tplData);
		if($json === false){
			Bd_Log::warning("json_encode failed");
		}

        //设置json头
        Header('Content-type:application/json; charset=UTF-8');

        $etag = '"' . md5 ($json) . ':' . dechex (strlen ($json)) . '"';
        //检测缓存
        if (isset ($_SERVER['HTTP_IF_NONE_MATCH']) && strlen ($_SERVER['HTTP_IF_NONE_MATCH']) > 0) {
            if ($etag == $_SERVER['HTTP_IF_NONE_MATCH'] || "W/".$etag == $_SERVER['HTTP_IF_NONE_MATCH']) {
                Hk_Util_Log::setLog('http_etag', 1);
                header ('ETag: ' . $etag, true, 304);
                return true;
            }
        }
        header ('ETag: ' . $etag);

        echo $json;
        return true;
    }

    //统计处理
    protected function _processLog() {

        $this->logVars['api_version'] = $this->_apiVersion;
        $this->logVars['api_module'] = $this->_controllor;
        $this->logVars['api_action'] = $this->_actionName;
        $this->logVars['pro_errno'] = $this->_tplData['errNo'];
        $this->logVars['pro_un'] = $this->_userInfo['uname'];
        $this->logVars['pro_uid'] = $this->_userInfo['uid'];
        $this->logVars['app_version'] = $this->_appVersion;
        $this->logVars['app_terminal'] = $this->_appTerminal;
        if (empty($this->logVars['pv'])) {
            $this->logVars['pv'] = sprintf("%s/%s/%s",$this->_controllor, $this->_apiVersion, $this->_actionName);
        }

        foreach ($this->logVars as $key => $value) {
            Hk_Util_Log::setLog($key, $value);
        }

        $idc = Bd_Conf::getConf('idc/cur');
        Hk_Util_Log::setLog('idc', $idc);

        Hk_Util_Log::printLog();
    }

    protected function _antispam() {
        $appId    = strval($this->_requestParam['appid']);
        $antispam = new Hk_Service_Antispam($appId);
        $antispam->sdkCheck();
        return;

        if ($this->_appId == '' || $this->_appId == 'homework') {
            $isZYBApp = true;
            $deviceId = strval($this->_requestParam['cuid']);
        } else {
            $deviceId = $this->_appId.'_'.strval($this->_requestParam['cuid']);
            $isZYBApp = false;
        }
        $conf = Bd_Conf::getConf ('/hk/antispam/common');

        $need = true;
        $vcheck = false;
        if ($this->_appType == 'android') {
            $vcheck = true;
            if (intval ($conf['switch']['android']) == 0) {
                $need = false;
                Hk_Util_Log::setLog ('common_antispam', 'NOT need due to android');
            }
        }

        if ($this->_appType == 'ios') {
            $vcheck = true;
            if (intval ($conf['switch']['ios']) == 0) {
                $need = false;
                Hk_Util_Log::setLog ('common_antispam', 'NOT need due to ios');
            }
        }

        //other version
        if (!$vcheck) {
            if (intval ($conf['switch']['other']) == 0) {
                $need = false;
                Hk_Util_Log::setLog ('common_antispam', 'NOT need due to other version');
            }
        }

        //排除uri
        if ($need) {
            $uri = strtolower ($this->getRequest ()->getRequestUri ());
            foreach ($conf['except'] as $exuri) {
                if ($uri == $exuri) {
                    $need = false;
                    Hk_Util_Log::setLog ('common_antispam', 'NOT need due to uri except');
                    break;
                }
            }
        }
        if ($need) {
            //内网调试
            if(Hk_Util_Ip::isInnerIp())
            {
                if(trim($_COOKIE['APP_DEBUG']) == strval(date('Y').(date('n') - 1))
                    || trim($_GET['skip']) == 'rdqa')
                {
                    $need = false;
                    Hk_Util_Log::setLog ('common_antispam', 'NOT need due to internal debug');
                }
            }
        }

        //校验
        if ($need) {
            if (!isset ($this->_requestParam['cuid']) || strlen ($this->_requestParam['cuid']) == 0) {
                Hk_Util_Log::setLog ('common_antispam', 'FAIL due to cuid is empty');
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::ANTISPAM_SIGNERR);
            }
            if (!isset ($this->_requestParam['sign']) || strlen ($this->_requestParam['sign']) == 0) {
                Hk_Util_Log::setLog ('common_antispam', 'FAIL due to sign is empty');
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::ANTISPAM_SIGNERR);
            }
            if (!isset ($this->_requestParam['appid']) || strlen ($this->_requestParam['appid']) == 0) {
                Hk_Util_Log::setLog ('common_antispam', 'FAIL due to appid is empty');
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::ANTISPAM_SIGNERR);
            }
            //IOS使用固定token Android使用注册信息
            $appid = strval ($this->_requestParam['appid']);
            $cuid = strval ($this->_requestParam['cuid']);
            $spamDs  = Hk_Ds_User_Antispam::getInstance();
            $devInfo = $spamDs->getRandToken($appid, $cuid);
            if (false === $devInfo) {
                Hk_Util_Log::setLog ('common_antispam', 'FAIL due to devInfo is empty');
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::ANTISPAM_SIGNERR);
            }
            $signPack = $this->_requestParam;
            unset ($signPack['sign']);
            ksort ($signPack);
            $signStr = '';
            foreach ($signPack as $k => $v) {
                $signStr .= $k . '=' . $v;
            }

            if($this->_appType != 'ios' && (intval($this->_requestParam['vc']) >= 119 || !$isZYBApp)) {
                $prefix = Bd_Conf::getConf ('/hk/antispam/keystore/key3');
                $signStr = $prefix . '[' . md5($devInfo['randomKey']) . ']@' . base64_encode ($signStr);
            }else {
                $signStr = '[' . $devInfo['randomKey'] . ']@' . base64_encode ($signStr);
            }
            $signHash = md5 ($signStr);
            if ($signHash != $this->_requestParam['sign']) {
                Hk_Util_Log::setLog ('common_antispam', 'FAIL due to hash error');
                throw new Hk_Util_Exception(Hk_Util_ExceptionCodes::ANTISPAM_SIGNERR);
            }

            Hk_Util_Log::setLog ('common_antispam', 'PASS');
        }
    }

    public function execute() {
        Hk_Util_Log::start('ts_all');

        try{
            Hk_Util_Log::start('ts_init');
            $this->_init();
            Hk_Util_Log::stop('ts_init');

            Hk_Util_Log::start('ts_antispam');
            $this->_antispam();
            Hk_Util_Log::stop('ts_antispam');

            Hk_Util_Log::start('ts_before');
            $ret = $this->_before();
            Hk_Util_Log::stop('ts_before');

            if ($ret === true) {

                Hk_Util_Log::start('ts_invoke');
                $res = $this->invoke();
                $this->_tplData['data'] = is_array($res) ? $res : array();
                Hk_Util_Log::stop('ts_invoke');

                Hk_Util_Log::start('ts_after');
                $ret = $this->_after();
                Hk_Util_Log::stop('ts_after');
            }
        } catch (Hk_Util_Exception $e) {
            $this->_tplData['errNo'] = $e->getErrNo();
            $this->_tplData['errstr'] = $e->getErrStr();
        }

        //输出
        Hk_Util_Log::start('ts_display');
        $this->_display();
        Hk_Util_Log::stop('ts_display');

        Hk_Util_Log::stop('ts_all');

        //打印日志
        $this->_processLog();
    }
}
