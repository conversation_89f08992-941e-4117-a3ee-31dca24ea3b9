<?php

interface Ext_Prometheus_Storage_Adapter {
    const COMMAND_INCREMENT_INTEGER = 1;
    const COMMAND_INCREMENT_FLOAT = 2;
    const COMMAND_SET = 3;

    /**
     * @return Ext_Prometheus_MetricFamilySamples[]
     */
    public function collect();

    public function updateHistogram(array $data);

    public function updateGauge(array $data);

    public function updateCounter(array $data);
}
