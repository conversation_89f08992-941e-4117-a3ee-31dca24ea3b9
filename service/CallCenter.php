<?php
/***************************************************************************
 * 
 * Copyright (c) 2017 Zuoyebang.com, Inc. All Rights Reserved
 * 
 **************************************************************************/

/**
 * @file Chuanglan.php
 * <AUTHOR>
 * @date 2017/04/23
 * @brief 呼叫中心服务调用-华云天下
 *  
 **/

class Hkzb_Service_CallCenter {
    //const SERVICE_DOMAIN_OL   = 'http://callcentermis.zuoyebang.cc'; //192.168.7.240:8090
    const SERVICE_DOMAIN_OL   = 'http://mis.zuoyebang.cc'; // 更改为mis域名
    const SERVICE_DOMAIN_TEST = 'http://test26.suanshubang.com'; //192.168.7.240:8090

    const SERVICE_BASE_URL = 'http://172.16.20.232:8080/paas'; //服务基础请求URL
    const CLIENT_ID        = '5a0082c7-6055-416e-8d19-ee6048a08b29'; //认证用账号
    const CLIENT_SECRET    = 'bc0ad2b1-d75f-43a1-b3d7-562a46477c27'; //认证用密码

    private $_appId       = '';
    private $_appSecret   = '';
    private $_accessToken = null;

    const ERR_SUCC             = 0;
    const ERR_UNKOWN           = 999;
    const ERR_PARAM_INVALID    = 1;
    const ERR_METHOD_INVALID   = 2;
    const ERR_RESPONSE_FALSE   = 11;
    const ERR_RESPONSE_INVALID = 12;
    const ERR_PEER_ERR         = 1000; //下游调用方返回错误，具体看data数据

    //区别通话记录来源 在
    const CC_RECORD_SOURCE_RONGLIANOLD = 0;//旧版容联话务中心
    const CC_RECORD_SOURCE_HUAYUN      = 4;//华云话务中心
    const CC_RECORD_SOURCE_RONGLIAN    = 8;//容联vip投诉专线
    
    const CC_SERVICE_TYPE_RL  = 0;  // 荣联 
    const CC_SERVICE_TYPE_HY  = 1;  // 华云
    const CC_SERVICE_TYPE_APP = 2;  // 手机app


    // 坐席状态
    const STATUS_LOGIN         = 0; // 登录
    const STATUS_FREE          = 1; // 置闲
    const STATUS_WORK          = 2; // 工作
    const STATUS_BUSY          = 3; // 置忙
    const STATUS_RING          = 4; // 振铃
    const STATUS_LOCK          = 5; // 锁定
    const STATUS_FINISH        = 6; // 事后整理
    const STATUS_LOGOUT        = 7; // 登出
    const STATUS_CHALENGE      = 8; // 挑战


    public function __construct($appId='rd_test', $appSecret='TODO：') {
        if(!in_array($appId,['yk_sale'])) {
            if (empty($this->accessToken)) {
                $this->makeAccessToken();
            }
        }
        $this->setToken($appId, $appSecret);
    }

    public function setToken($appId, $appSecret) {
        $this->_appId     = $appId;
        $this->_appSecret = $appSecret;
    }

    public function makeAccessToken(){
        $url = self::SERVICE_BASE_URL.'/accreditation';
        $data = array(
            'clientId' => self::CLIENT_ID,
            'clientScr' => self::CLIENT_SECRET,
        );
        $data = json_encode($data);

        $res = self::_curlPost($url, $data);
        $res = @json_decode($res, true);
        if(empty($res) || !is_array($res) || $res['status'] != 0 || empty($res['message'])){
            Bd_Log::warning("makeAccessToken error: ".@json_encode($res));
            return false;
        }

        $this->_accessToken = strval($res['message']);
        return true;
    }

    private static function _curlPost($url, $data){
        //初始化curl
        $ch = curl_init();

        //参数设置
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type:application/json'));
        //curl_setopt($ch, CURLOPT_HEADER, 0);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 2);
        curl_setopt($ch, CURLOPT_TIMEOUT, 5);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

        $result = curl_exec($ch);
        Bd_Log::notice("use_curl:url:%s", curl_getinfo($ch, CURLINFO_EFFECTIVE_URL));


        //连接失败
        if($result === false){
            $errNo = curl_errno($ch);
            $errMsg = curl_error($ch);
            Bd_Log::warning("Error[service callcenter connect error] Detail[errno:$errNo errmsg:$errMsg url:".$url." params:".$data."]");
        }
        curl_close($ch);

        return $result;
    }
    //>>>>以下为新回调，上述代码最终会下线，请勿调用>>>>

    /**
     * @desc 呼出
     * @param int   $uid
     * @param string $callNum   呼出的目标号码
     * @param int $serveType  呼叫类型：  0:荣联 1:华云 2:手机app
     * @return {errNo, errStr, data:视具体请求而定}
     **/
    public function callOut($uid, $calledNum, $serveType = 1) {
        $ret = array('errNo' => self::ERR_UNKOWN, 'errStr' => '未知错误', 'data'   => array());
        $method = 'callout';
        // 手机app 新开一个接口
        if ($serveType == self::CC_SERVICE_TYPE_APP) {
            $method = 'calloutapp';
        }

        if ( (0 >= $uid) || ('' == $calledNum) ) {
            Bd_Log::warning("Error[CallCenter] Abstract[method_{$method}] Detail[params invalid]");
            $ret['errNo']  = self::ERR_PARAM_INVALID;
            $ret['errStr'] = $this->parseErrStr($ret['errNo']);
            return $ret;
        }

        $params = array(
            'callingUid'  => intval($uid),
            'calledNum'   => strval($calledNum),
            'serveType' => intval($serveType),
        );

        Hk_Util_Log::start('cc_callout');
        $result = $this->sendRequestToCC($method, $params);
        Hk_Util_Log::stop('cc_callout');

        $ret['errNo']  = $result['errNo'];
        $ret['errStr'] = $result['errStr'];
        if (self::ERR_SUCC === $result['errNo']) {
            $ret['data']  = $result['response'];
        } elseif (self::ERR_PEER_ERR === $result['errNo']) {
            $ret['data']['errNo']  = $result['response']['errNo'];
            $ret['data']['errStr'] = $result['response']['errStr'];
        }

        return $ret;
    }
    


    /**
     * @desc method到url的转换
     * @param string $method 请求的方法名
     * @return string url
     **/
    private function parseMethodToUrl($method) {
        $method = strtolower($method);
        $arr = array(
            'callout'    => '/call/callout',
            'calloutapp' => '/call/calloutapp',
        );
        $url = '';
        if (isset($arr[$method])) {
            $idc = ral_get_idc();
            $domain = self::SERVICE_DOMAIN_TEST;
            if ('yun' === $idc) {
                $domain = self::SERVICE_DOMAIN_OL;
            }
            $url = $domain . '/callcenter'.$arr[$method];
        }
        return $url;
    }


    /**
     * @desc 请求封装
     * @param string $method 请求的方法名
     * @param array  $data   请求的参数
     * @return {errNo, errStr, response:{}}
     **/
    private function sendRequestToCC($method, $data = array()) {
        $ret = array('errNo' => self::ERR_UNKOWN, 'errStr' => '未知错误', 'response'   => array());
        do {
            if (empty($method) || !is_array($data) || empty($data)){
                Bd_Log::warning("Error[CallCenter] Abstract[method_{$method}] Detail[params invalid]");
                $ret['errNo'] = self::ERR_PARAM_INVALID;
                break;
            }
            $url = $this->parseMethodToUrl($method);
            if ('' == $url) {
                Bd_Log::warning("Error[CallCenter] Abstract[method_{$method}] Detail[method undefine]");
                $ret['errNo'] = self::ERR_METHOD_INVALID;
                break;
            }
            $data = $this->_parseAppSign($data);

            //发送POST请求
            Hk_Util_Log::start('cc_{$method}_req');
            $result = $this->_talkWithCC($url, $data);
            if (false === $result) {
                Bd_Log::warning("Error[CallCenter] Abstract[method_{$method}] Detail[no response]");
                $ret['errNo'] = self::ERR_RESPONSE_FALSE;
                break;
            }
            Hk_Util_Log::stop('cc_{$method}_req');

            $result = @json_decode($result, true);
            if (empty($result)) {
                Bd_Log::warning("Error[CallCenter] Abstract[method_{$method}] Detail[response invalid]");
                $ret['errNo'] = self::ERR_RESPONSE_INVALID;
                break;
            }

            if (0 === $result['errNo']) {
                $ret['errNo']    = self::ERR_SUCC;
                $ret['response'] = $result['data'];
            } else {
                $ret['errNo'] = self::ERR_PEER_ERR;
                $ret['response']['errNo']  = $result['errNo'];
                $ret['response']['errStr'] = $result['errStr'];
            }
        } while(0);

        $ret['errStr'] = $this->parseErrStr($ret['errNo']);
        return $ret;
    }


    /**
     * @和callcenter会话
     * @param unknown $url
     * @param unknown $data
     * @return mixed
     */
    private function _talkWithCC($url, $data) {
        //初始化curl
        $ch = curl_init();

        /*$idc = ral_get_idc();
        if ('test' === $idc) {
          // curl_setopt($ch, CURLOPT_PROXY, "proxy.zuoyebang.com:80");
        }*/

        $qs = http_build_query($data);

        //参数设置
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 2);
        curl_setopt($ch, CURLOPT_TIMEOUT, 5);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $qs);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);

        $result = curl_exec($ch);
        Bd_Log::notice("use_curl:url:%s", curl_getinfo($ch, CURLINFO_EFFECTIVE_URL));


        //连接失败
        if($result === false){
            $errNo = curl_errNo($ch);
            $errStr = curl_error($ch);
            Bd_Log::warning("Error[CallCenter] Abstract[curlFail] Detail[errNo:{$errNo} errStr:{$errStr} url:{$url}]");
        }
        curl_close($ch);

        return $result;
    }


    private function _parseAppSign(Array $signPack) {
        if (!empty($signPack)) {
            unset($signPack['appSign']);
            ksort($signPack);
            $signStr  = '';
            foreach ($signPack as $k => $v) {
                $signStr .= $k . '=' . $v;
            }
            $signStr = '[' . $this->_appId . ':' . $this->_appSecret . ']@' . base64_encode($signStr);
            $signPack['appId']   = strval($this->_appId);
            $signPack['appSign'] = md5($signStr);
        }

        return $signPack;
    }


    private function parseErrStr($errNo) {
        $arr = array(
            self::ERR_SUCC             => '成功',
            self::ERR_UNKOWN           => '未知错误',
            self::ERR_PARAM_INVALID    => '参数错误',
            self::ERR_METHOD_INVALID   => '方法错误',
            self::ERR_RESPONSE_FALSE   => 'cc无应答',
            self::ERR_RESPONSE_INVALID => 'cc返回错误',
            self::ERR_PEER_ERR         => 'cc业务异常',
        );
        $err = isset($arr[$errNo])? $arr[$errNo] : '未定义错误';
        return $err;
    }

    //业务线交互

    /**
     * 查询当前坐席的状态
     * @param string $strAppId 呼叫中心应用Id
     * @param integer $intUid  所查询的座席的Id
     * @return false|integer
     */
    public static function getAgentStatus($strAppId,$intUid){
        $strAppId = (string) $strAppId;
        $intUid   = (integer) $intUid;
        if(empty($strAppId) || empty($intUid)){
            Bd_Log::warning("Error[CallCenter][Param Error] Detail[appId:{$strAppId},uid:{$intUid}]");
            return false;
        }

        //ral调用fudao_scmis模块
        $header = [
            'pathinfo'    => '/callcenter/rest/queryagentstatus',
            'querystring' => "appId={$strAppId}&uid={$intUid}",
        ];

        $ralRes = ral('fudao_scmis', 'get', [], rand(), $header);
        $ralRes = @json_decode($ralRes, true);

        //判断业务线的响应
        if (!$ralRes) {
            //未获得响应
            $errno           = ral_get_errno();
            $errmsg          = ral_get_error();
            $protocol_status = ral_get_protocol_code();
            Bd_Log::warning("Error:[Service miscourse Ral Failed],Detail:[res:" . @json_encode($ralRes, JSON_UNESCAPED_UNICODE) .
                ",errno:{$errno},errmsg:{$errmsg},protocol_status:{$protocol_status}]");
            return false;
        }

        if (!is_integer($ralRes["errNo"])) {
            //兼容我们的nginx在一些异常情况下，会返回302 在这种情况下errNo会为null 避免调用方没有强验证的情况
            Bd_Log::warning('Error[Service miscourse Ral Failed],Detail:[errNo is not integer]');
            return false;
        }

        $strRes = json_encode($ralRes,JSON_UNESCAPED_UNICODE);
        if($ralRes["errNo"] != 0){
            Bd_Log::warning("Error[Service miscourse Ral Error],Detail:[strRes:{$strRes}]");
            return false;
        }
        Bd_Log::addNotice("callcenter_ralRes",json_encode($ralRes));

        return (integer) $ralRes["data"]["status"];
    }
}
