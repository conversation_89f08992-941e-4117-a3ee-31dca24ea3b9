<?php
/**
 * @file Material.php
 * @Description
 * @Date 2021/07/02 11:51 下午
 * @Created by zhangchangguo<PERSON>@zuoyebang.com
 */

class Qdlib_Service_Hetu_Material
{
    protected $objMaterial;
    protected $objMaterialCategory;

    public function __construct()
    {
        $this->objMaterial = new Qdlib_Ds_Hetu_Material();
        $this->objMaterialCategory = new Qdlib_Ds_Hetu_MaterialCategory();
    }

    /*
     * 查询素材
     */
    public function getList($arrInput)
    {
        $arrConds = [];
        if(isset($arrInput['name']) && strlen(trim($arrInput['name'])) > 0)
        {
            $arrConds['name'] = ['%'.trim($arrInput['name']).'%', 'like'];
        }
        if(intval($arrInput['projectLv2']) > 0)
        {
            $arrConds['projectLv2List'] = ['%|'.intval($arrInput['projectLv2']).'|%', 'like'];
        }
        foreach(['suppliesType', 'materialKind', 'usedStatus', 'checkStatus'] as $k)
        {
            if(intval($arrInput[$k]) > 0)
            {
                $arrConds[$k] = intval($arrInput[$k]);
            }
        }
        if(!isset($arrConds['checkStatus']))
        {
            $arrConds[] = 'check_status in ('.Qdlib_Const_Hetu_Material::CHECK_STATUS_OK.','.Qdlib_Const_Hetu_Material::CHECK_STATUS_REJECT.')';
        }
        if(empty($arrInput['all']))
        {
            $arrConds['categoryId'] = intval($arrInput['categoryId']);
        }
        $page = (intval($arrInput['page']) >= 1) ? intval($arrInput['page']) : 1;
        $pageSize = (intval($arrInput['pageSize']) > 0) ? intval($arrInput['pageSize']) : 10;
        $start = ($page - 1) * $pageSize;
        $arrAppends = array(
            'order by update_time desc',
            "limit $start, $pageSize",
        );
        $total = $this->objMaterial->getMaterialTotal($arrConds);
        if (empty($total)) {
            $list = [];
        }
        else
        {
            $list = $this->objMaterial->getMaterialList($arrConds, [], $arrAppends);
        }
        if(count($list) > 0)
        {
            $projectLv2Map = Qdlib_Service_Toufangmis_Project::getProjectLv2List();
            array_walk($list, function(&$row) use ($projectLv2Map) {
                $row['createTime'] = date("Y-m-d H:i:s",$row['createTime']);
                $row['updateTime'] = date("Y-m-d H:i:s",$row['updateTime']);
                $row['modifyTime'] = date("Y-m-d H:i:s",$row['modifyTime']);
                $row['usedStatuStr'] = Qdlib_Const_Hetu_Material::$usedStatusMap[$row['usedStatus']];
                $row['checkStatusStr'] = Qdlib_Const_Hetu_Material::$checkStatusMap[$row['checkStatus']];
                $row['suppliesTypeStr'] = Qdlib_Const_Hetu_Material::$suppliesTypeMap[$row['suppliesType']];
                $row['materialKindStr'] = Qdlib_Const_Hetu_Material::$materialKindMap[$row['materialKind']];
                $row['projectLv2List'] = array_intersect_key($projectLv2Map, array_flip(explode('|', trim($row['projectLv2List'], '|'))));
                if($row['materialKind'] == Qdlib_Const_Hetu_Material::MATERIA_KIND_VIDEO) {
                    $con_chr = (strpos($row['url'], '?') === false) ? '?' : '&';
                    $row['cover'] = $row['url'].$con_chr.'ci-process=snapshot&time=1&format=jpg';
                }
            });
        }
        return array(
            'total'     => $total,
            'page'      => $page,
            'list'      => $list,
        );
    }

    /*
     * 输入多个文件夹，按照文件目录树形式组织素材
     */
    public function getListByCategory($arrInput)
    {
        $arrConds = [];
        $arrInput['categoryId'] = array_filter(array_map(intval, (array)$arrInput['categoryId']), function($v){return $v > 0;});
        if (count($arrInput['categoryId']) > 0) {
            //找到输入文件夹及其所有子孙
            $categoryId_list = $this->objMaterialCategory->getDescendants($arrInput['categoryId']);
            //如果输入文件夹及子孙为空,说明输入文件夹不存在,直接返回
            if(count($categoryId_list) == 0)
            {
                return [];
            }
            $arrConds[] = 'category_id in ('.implode(',', $categoryId_list).')';
            if(isset($arrInput['name']) && strlen(trim($arrInput['name'])) > 0)
            {
                $arrConds['name'] = ['%'.trim($arrInput['name']).'%', 'like'];
            }
            if(intval($arrInput['projectLv2']) > 0)
            {
                $arrConds['projectLv2List'] = ['%|'.intval($arrInput['projectLv2']).'|%', 'like'];
            }
            foreach(['suppliesType', 'materialKind', 'usedStatus', 'checkStatus'] as $k)
            {
                if(intval($arrInput[$k]) > 0)
                {
                    $arrConds[$k] = intval($arrInput[$k]);
                }
            }
            if(!isset($arrConds['checkStatus']))
            {
                $arrConds[] = 'check_status in ('.Qdlib_Const_Hetu_Material::CHECK_STATUS_OK.','.Qdlib_Const_Hetu_Material::CHECK_STATUS_REJECT.')';
            }
            $m_list = $this->objMaterial->getMaterialList($arrConds, ['id','name','url','materialKind','categoryId'], null);
            //找出最终存在的category集合
            $leaf_categroyId_list = array_unique(array_column($m_list, 'categoryId'));
            $last_categoryId_list = $this->objMaterialCategory->getAncestors($leaf_categroyId_list);
            if(count($last_categoryId_list) == 0)
            {
                return [];
            }
            //生成节点树
            $nodeTreeMap = $this->objMaterialCategory->genCategoryNodeTreeMap($last_categoryId_list);
            //挂载素材
            foreach($m_list as $row)
            {
                $categoryId = $row['categoryId'];
                if(isset($nodeTreeMap[$categoryId]))
                {
                    $nodeTreeMap[$categoryId]->materials[] = $row;
                }
            }
            //返回结果
            $ret = [];
            foreach($arrInput['categoryId'] as $categoryId)
            {
                if(isset($nodeTreeMap[$categoryId]))
                {
                    $ret[] = $nodeTreeMap[$categoryId];
                }
            }
            return $ret;
        }
        return [];
    }

    /*
     * 更新素材(使用状态)
     */
    public function update($arrInput)
    {
        $arrInput['id'] = array_map(intval, (array)$arrInput['id']);
        if (intval($arrInput['usedStatus']) > 0 && count($arrInput['id']) > 0) {
            $arrFields['usedStatus'] = intval($arrInput['usedStatus']);
            $arrConds[] = 'id in ('.implode(',', $arrInput['id']).')';
            $ret = $this->objMaterial->updateMaterial($arrConds, $arrFields);
            if ($ret !== false) {
                return ['affectedRows' => $ret];
            }
        }
        throw new Qdlib_Common_Exception(Qdlib_Common_ExceptionCodes::DB_ERROR, '更新素材使用状态失败');
    }

    /*
     * 以所属的批量文件夹及父文件夹为条件，批量更新素材(使用状态)
     */
    public function updateByCategory($arrInput)
    {
        $arrInput['categoryId'] = array_filter(array_map(intval, (array)$arrInput['categoryId']), function($v){return $v > 0;});
        if (intval($arrInput['usedStatus']) > 0 && count($arrInput['categoryId']) > 0) {
            //找到输入文件夹及其所有子孙
            $categoryId_list = $this->objMaterialCategory->getDescendants($arrInput['categoryId']);
            if(count($categoryId_list) > 0)
            {
                $arrFields['usedStatus'] = intval($arrInput['usedStatus']);
                $arrConds[] = 'category_id in ('.implode(',', $categoryId_list).')';
                $ret = $this->objMaterial->updateMaterial($arrConds, $arrFields);
                if ($ret !== false) {
                    return ['affectedRows' => $ret];
                }
            }
        }
        throw new Qdlib_Common_Exception(Qdlib_Common_ExceptionCodes::DB_ERROR, '更新素材使用状态失败');
    }

    /*
     * 更新素材所属文件夹
     */
    public function move($arrInput)
    {
        $arrInput['id'] = array_map(intval, (array)$arrInput['id']);
        if (isset($arrInput['categoryId']) && count($arrInput['id']) > 0) {
            $ret = $this->objMaterial->updateCategoryIdOfMaterial($arrInput['id'], intval($arrInput['categoryId']));
            if ($ret !== false) {
                return ['affectedRows' => $ret];
            }
        }
        throw new Qdlib_Common_Exception(Qdlib_Common_ExceptionCodes::DB_ERROR, '更新素材所属文件夹失败');
    }
}