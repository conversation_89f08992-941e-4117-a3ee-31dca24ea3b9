<?php
/***************************************************************************
 *
 * Copyright (c) 2017 Zuoyebang, Inc. All Rights Reserved
 *
 **************************************************************************/


/**
 * @file   Sku.php
 * <AUTHOR>
 * @date   2018/7/2 下午10:06
 * @brief
 **/
 
 
class Zb_Service_Stock_Stock {  
    
    const RAL_SERVICE_ZBBIZ = 'zbstock';
    
    # 库存统一返回错误码
     const ERROR_IDEMPOTENT_1001 = 1001; //命中 TRY 幂等锁
     const ERROR_IDEMPOTENT_1002 = 1002; //命中 CONFIRM 幂等锁
     const ERROR_IDEMPOTENT_1003 = 1003; //命中 CANCEL 幂等锁
    
    /**
     * 创建实物库存
     * @param type $arrInput
     *  [
     *      entityStockList => [
     *          entityId    => 1, 
                originStock => 3
     *     ],
     *     entityType  => 2,
     *     operatUid => 11
     *     operator => 'syl' 
     * ]
     *      
     * @param type $options
     * @return array
     */
    public static function createEntityStock($arrInput) 
    {
        $arrHeader = array(
            'pathinfo'  => "/zbstock/interface/createentitystock",
            'refer'     => $_SERVER['REQUEST_URI'],
        ); 
        $arrParams = array(
            'operatUid'       => (int)$arrInput['operatUid'],
            'operator'        => trim($arrInput['operator']),
            'entityStockList' => $arrInput['entityStockList'],
            'entityType'      => (int)$arrInput['entityType'],
        ); 

        $data = Zb_Util_ZbServiceTools::requestApi (self::RAL_SERVICE_ZBBIZ,Zb_Util_ZbServiceTools::METHOD_POST,$arrParams,$arrHeader) ;
        return json_encode($data);
    } 
    
    /**
     * 更改实物总库存量 | 注：不是再历史数据做垒加
     * @param type $arrInput
     * @param type $options
     * @return type
     */
    public static function updateEntityStock($arrInput)
    {
        $arrHeader = array(
            'pathinfo' => "/zbstock/interface/updateentitystock",
            'refer'    => $_SERVER['REQUEST_URI'],
        ); 
        $arrParams = array(
            'operatUid'       => (int)$arrInput['operatUid'],
            'operator'        => trim($arrInput['operator']),
            'entityStockList' => $arrInput['entityStockList'],
            'entityType'      => (int)$arrInput['entityType'],
        ); 
        
        $data = Zb_Util_ZbServiceTools::requestApi (self::RAL_SERVICE_ZBBIZ,Zb_Util_ZbServiceTools::METHOD_POST, $arrParams, $arrHeader);
        return json_encode($data);
    }
    
    /**
     * 批量拉取某类型下多个实物id的库存信息
     * @param type $arrInput
     * @param type $options
     * @return type
     */
    public static function getEntityStock($arrInput)
    {
        $arrHeader = array(
            'pathinfo' => "/zbstock/interface/getentitystock",
            'refer'    => $_SERVER['REQUEST_URI'],
        ); 
        $arrParams = array(
            'entityIds'  => $arrInput['entityIds'], 
            'entityType' => (int)$arrInput['entityType'],
	        'isCache'    => isset($arrInput['isCache']) ? boolval($arrInput['isCache']) : true,
        ); 

        $data = Zb_Util_ZbServiceTools::requestApi (self::RAL_SERVICE_ZBBIZ,Zb_Util_ZbServiceTools::METHOD_POST, $arrParams, $arrHeader);
        return json_encode($data);
    }
    
    /**
     * 分布式库存操作 - TRY 预锁定库存
     * @param type $arrInput 
            'typeEntityList'  => [{"entityId":1000002,"cnt":1}]  申请锁定实物 & 锁定量 & 实物类型
            'uniqueTccId'     => (int)$arrInput['uniqueTccId'],                 业务线标记TCC唯一ID
            'entityType'    => (int)$arrInput['entityType'],                实物类型
     *      type 是否锁定库存
     * @return type
     */
    public static function tccTryEntityStock($arrInput)
    {
        $arrHeader = array(
            'pathinfo' => "/zbstock/interface/tcctrycacheentitystock",
            'refer'    => $_SERVER['REQUEST_URI'],
        );   
        $arrParams = array(
            'typeEntityList'  => $arrInput['typeEntityList'], 
            'uniqueTccId'     => (int)$arrInput['uniqueTccId'],  
            'entityType'      => (int)$arrInput['entityType'],  
            'type'            => !isset($arrInput['type']) ? 0 : intval($arrInput['type']),
	        'lockStockType'   => isset($arrInput['lockStockType']) ? (int)$arrInput['lockStockType'] : Zb_Const_Dak::LOCK_STOCK_COMMON_TYPE,
        ); 

        $data = Zb_Util_ZbServiceTools::requestApi (self::RAL_SERVICE_ZBBIZ,Zb_Util_ZbServiceTools::METHOD_POST, $arrParams, $arrHeader);
        return json_encode($data);
    }
    
    /**
     * 分布式库存操作 - Confirm 库存核销
     * @param type $arrInput
     *      uniqueTccId   业务线标记TCC唯一ID
     *      entityType    业务线类型
     * @param type $options
     * @return type 
     */
    public static function tccConfirmEntityStock($arrInput)
    {
        $arrHeader = array(
            'pathinfo' => "/zbstock/interface/tccconfirmcacheentitystock",
            'refer'    => $_SERVER['REQUEST_URI'],
        );   
        $arrParams = array( 
            'uniqueTccId'     => (int)$arrInput['uniqueTccId'],  
            'entityType'      => (int)$arrInput['entityType'],  
        ); 

        $data = Zb_Util_ZbServiceTools::requestApi (self::RAL_SERVICE_ZBBIZ,Zb_Util_ZbServiceTools::METHOD_POST,$arrParams, $arrHeader);
        return json_encode($data);
    }
    
    /**
     * 分布式库存操作 - Cancel 库存核销
     * @param type $arrInput
     *      uniqueTccId   业务线标记TCC唯一ID
     *      entityType    业务线类型
     * @param type $options
     * @return type 
     */
    public static function tccCancelEntityStock($arrInput)
    {
        $arrHeader = array(
            'pathinfo' => "/zbstock/interface/tcccancelcacheentitystock",
            'refer' => $_SERVER['REQUEST_URI'],
        );   
        $arrParams = array( 
            'uniqueTccId'     => (int)$arrInput['uniqueTccId'],  
            'entityType'      => (int)$arrInput['entityType'],  
        ); 

        $data = Zb_Util_ZbServiceTools::requestApi (self::RAL_SERVICE_ZBBIZ,Zb_Util_ZbServiceTools::METHOD_POST, $arrParams, $arrHeader);
        return json_encode($data);
    }
}