#!/bin/sh
APP_NAME="hkzb"
PRE_PATH_PHPLIB="php/phplib/hkzb"
PRE_PATH_CONF="conf/hkzb"

rm -rf output

mkdir -p output/$PRE_PATH_PHPLIB
mkdir -p output/$PRE_PATH_CONF

cp -r const dao ds service util output/$PRE_PATH_PHPLIB
cp -r conf/* output/$PRE_PATH_CONF
cd output

find ./ -type d -name .svn |xargs -i rm -rf {}
find ./  -name "*.php~" |xargs -i rm -rf {}
find ./ -name "*.sh~" | xargs -i rm -rf {}


tar -zcf $APP_NAME.tar.gz  $PRE_PATH_PHPLIB $PRE_PATH_CONF

rm -rf  php conf
