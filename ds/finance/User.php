<?php

class Qdlib_Ds_Finance_User extends Qdlib_Ds_Finance_FinanceBase
{
    const DAOOBJ = 'Qdlib_Dao_Finance_User';
    const STOPED  = 1;
    const STARTED = 2;

    public function __construct()
    {
        $daoObj = self::DAOOBJ;
        $this->_tableDaoObj = new $daoObj();
    }

    protected function getFormatDbData($arrParams) {
        $arrFields = array(
            'id'         => isset($arrParams['id']) ? intval($arrParams['id']) : 0,
            'userEmail'  => isset($arrParams['userEmail']) ? strval($arrParams['userEmail']) : '',
        );
        return $arrFields;
    }
}
