<?php
/**************************************************************************
 *
 * Copyright (c) 2018 Zuoyebang.com, Inc. All Rights Reserved
 *
 **************************************************************************/

/**
 * @file   OperationWeiKeExt.php
 * <AUTHOR>
 * @date   2018/11/15 16:35
 * @brief  名师微课0.2
 **/
class Oplib_Ds_OperationWeiKeExt
{
    const ALL_FIELDS       = 'id,weikeId,lubokeId,weight,scene,type,typeSort,difficulty,createTime,ext';
    const SHOW_BASE_FIELDS = 'id,weikeId,lubokeId,scene,type,typeSort';

    const TYPE_GAINIANKE = 1; // 概念课
    const TYPE_JIETIKE   = 2; // 解题课

    const SCENE_GAINIANJIANGJIE = 1; // 概念讲解
    const SCENE_GONGGULIANXI    = 2; // 巩固练习
    const SCENE_TUOZHANTISHENG  = 3; // 拓展提升

    public static $ARR_TYPE = [
        self::TYPE_GAINIANKE => '概念课',
        self::TYPE_JIETIKE   => '解题课',
    ];

    public static $ARR_SCENE = [
        self::SCENE_GAINIANJIANGJIE => '概念讲解',
        self::SCENE_GONGGULIANXI    => '巩固练习',
        self::SCENE_TUOZHANTISHENG  => '拓展提升',
    ];

    private $_objDaoWeiKeExt;

    public function __construct()
    {
        $this->_objDaoWeiKeExt = new Oplib_Dao_OperationWeiKeExt();
    }

    /** 根据type类型获取type的描述
     * @param $intType
     * @return mixed|null
     */
    public static function getTypeDesc($intType)
    {
        if (array_key_exists($intType, self::$ARR_TYPE)) {
            return self::$ARR_TYPE[$intType];
        }
        return null;
    }

    /**根据scene类型获取scene的描述
     * @param $intScene
     * @return mixed|null
     */
    public static function getSceneDesc($intScene)
    {
        if (array_key_exists($intScene, self::$ARR_SCENE)) {
            return self::$ARR_SCENE[$intScene];
        }
        return null;
    }


    /** 根据weikeId和lubokeIds获取数据 如果lubokeIds为空，则只按weikeId一个条件查询数据
     * @param        $weikeId
     * @param array  $lubokeIds
     * @param string $strFields
     * @return array|false
     */
    public function getWeikeExtListByWeikeId($weikeId, $lubokeIds = [], $strFields = self::SHOW_BASE_FIELDS)
    {
        $arrConds = ['weikeId' => intval($weikeId)];
        if ($arrConds['weikeId'] <= 0) {
            Bd_Log::warning("Error:[param error],Detail:[weikeId:$weikeId]");
            return false;
        }
        if (!empty($lubokeIds) && is_array($lubokeIds)) {
            $lubokeIds  = array_map('intval', $lubokeIds);
            $lubokeIds  = array_unique($lubokeIds);
            $arrConds[] = 'luboke_id in (' . implode(',', $lubokeIds) . ')';
        }
        if (empty($strFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        } else {
            $arrFields = explode(',', $strFields);
        }

        $ret = $this->_objDaoWeiKeExt->getListByConds($arrConds, $arrFields);

        return $ret;
    }
    
    /** 根据lubokeId获取查询数据
     * @param        $lubokeId
     * @param string $strFields
     * @return bool
     */
    public function getWeikeExtByLubokeId($lubokeId, $strFields = self::ALL_FIELDS)
    {
        $arrConds = ['luboke_id' => intval($lubokeId)];
        if ($arrConds['luboke_id'] <= 0) {
            Bd_Log::warning("Error:[param error],Detail:[lubokeId:$lubokeId]");
            return false;
        }
        if (empty($strFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        } else {
            $arrFields = explode(',', $strFields);
        }

        $ret = $this->_objDaoWeiKeExt->getRecordByConds($arrConds, $arrFields, null, ['LIMIT 1']);

        return $ret;
    }
}