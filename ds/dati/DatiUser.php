<?php

/**
 * @file   DatiUser.php
 * <AUTHOR>
 * @date   2018-01-05
 * @brief  答题用户表
 **/
class Hkzb_Ds_Dati_DatiUser
{
    private $_objDaoDatiUser;

    public static $allFields = array(
        'uid',
        'resurgence',
        'totalMoney',
        'balance',
        'inviteCode',
        'inviterId',
        'mobile',
        'extData',
        'createTime',
        'updateTime',
    );

    public function __construct()
    {
        $this->_objDaoDatiUser = new Hkzb_Dao_Dati_DatiUser();
    }

    /**
     * 新增
     *
     * @param  array $arrParams 属性
     * @return bool true/false
     */
    public function addUser($arrParams)
    {
        $ret = false;
        if (empty($arrParams['uid'])) {
            Bd_Log::warning("Error:[param error], Detail:[" . json_encode($arrParams) . "]");

            return $ret;
        }

        $curTime = time();;
        $arrFields = array(
            'uid'        => $arrParams['uid'],
            'resurgence' => $arrParams['resurgence'] ? $arrParams['resurgence'] : 0,
            'totalMoney' => $arrParams['totalMoney'] ? $arrParams['totalMoney'] : 0,
            'balance'    => $arrParams['balance'] ? $arrParams['balance'] : 0,
            'inviteCode' => $arrParams['inviteCode'] ? $arrParams['inviteCode'] : Dati_Util::uidToStr($arrParams['uid']),
            'inviterId'  => $arrParams['inviterId'] ? $arrParams['inviterId'] : 0,
            'mobile'     => $arrParams['mobile'] ? $arrParams['mobile'] : '',
            'createTime' => $curTime,
            'updateTime' => $curTime,
        );

        if ($this->_objDaoDatiUser->insertRecords($arrParams['uid'], $arrFields)) {
            $ret = true;
        }

        return $ret;

    }

    /**
     * 更新
     *
     * @param  int $uid
     * @param array $data
     * @return  bool
     */
    public function updateInfoByUid($uid, $data)
    {
        $uid = intval($uid);
        if ($uid <= 0 || empty($data)) {
            Bd_Log::warning("Error:[param error], Detail:[$uid , " . json_encode($data) . "]");

            return false;
        }

        $arrConds           = array(
            'uid' => $uid,
        );
        $data['updateTime'] = time();

        if (false === $this->_objDaoDatiUser->updateByConds($uid, $arrConds, $data)) {
            Bd_Log::warning("Error:[update error], Detail:[$uid , " . json_encode($data) . "]");

            return false;
        }

        return true;
    }


    /**
     * 用户提现时资料添加 手机号
     *
     * @param  int $uid
     * @param string $mobile
     * @return  bool
     */
    public function addUserWithdrawInfo($uid, $mobile)
    {
        $uid = intval($uid);

        if ($uid <= 0 || empty($mobile)) {
            Bd_Log::warning("Error:[param error], Detail:[$uid ,$mobile]");

            return false;
        }

        $userInfo = $this->getInfoByUid($uid);
        if (empty($userInfo)) {
            // 没有信息时添加
            $data = [
                'uid'    => $uid,
                'mobile' => $mobile,
            ];
            $ret  = $this->addUser($data);
        } else {
            $data = array(
                'mobile' => strval($mobile)
            );
            $ret  = $this->updateInfoByUid($uid, $data);
            $this->resetUserInfoCacheByUid($uid); // 更新缓存
        }

        if (false === $ret) {
            Bd_Log::warning("Error:[edit user info error], Detail:[$uid, $mobile]");

            return false;
        }

        return $ret;
    }

    /**
     * 更新用户信息缓存
     *
     * @param  int $uid
     * @return  array
     */
    public function resetUserInfoCacheByUid($uid)
    {
        $uid = intval($uid);
        if ($uid == 0) {
            Bd_Log::warning("Error:[params error], Detail:[uid:$uid]");

            return false;
        }

        $arrFields = self::$allFields;
        $arrConds  = array(
            'uid' => $uid,
        );

        // 读主库
        $this->_objDaoDatiUser->startTransaction();
        $userInfo = $this->_objDaoDatiUser->getRecordByConds($uid, $arrConds, $arrFields);
        $this->_objDaoDatiUser->commit();

        if (empty($userInfo)) {
            Bd_Log::warning("Error:[user info error], Detail:[uid:$uid]");

            return false;
        }

        $objMemcache = new Hkzb_Ds_Dati_UserMemcache();
        $ret         = $objMemcache->setUserInfo($uid, $userInfo);

        return $ret;
    }

    /**
     * 获取记录
     *
     * @param  int $uid
     * @return  array
     */
    public function getInfoByUid($uid)
    {
        $ret       = array();
        $cacheFlag = 1;
        if (empty($uid)) {
            Bd_Log::warning("Error:[param error], Detail:[$uid]");

            return $ret;
        }

        $objMemcache = new Hkzb_Ds_Dati_UserMemcache();
        $ret         = $objMemcache->getUserInfo($uid);
        if (empty($ret)) {
            //没有缓存
            $cacheFlag = 0;

            $ret = $this->getInfoByUidFromDb($uid);
            if (!empty($ret)) {
                // 用户信息缓存到 Memcache
                $objMemcache->setUserInfo($uid, $ret);
            }
        }

        //标记缓存使用情况
        Bd_Log::addNotice('useCache', $cacheFlag);

        return $ret;
    }

    /**
     *  直接从数据库读数据
     *
     * @param  int $uid
     * @return  array
     */
    public function getInfoByUidFromDb($uid)
    {
        $ret = array();
        if (empty($uid)) {
            Bd_Log::warning("Error:[param error], Detail:[$uid]");

            return $ret;
        }

        $arrFields   = self::$allFields;
        $arrConds    = array(
            'uid' => $uid,
        );
        $objDatiUser = new Hkzb_Dao_Dati_DatiUser();
        $ret         = $objDatiUser->getRecordByConds($uid, $arrConds, $arrFields);

        return $ret;
    }

    /**
     * 获取复活卡数量
     *
     * @param  int $uid
     * @return  array
     */
    public function getIndexPageUserInfo($uid)
    {
        $uid = intval($uid);
        if ($uid == 0) {
            Bd_Log::warning("Error:[param error], Detail:[$uid]");

            return array();
        }

        $ret = array(
            'resurgence' => 0,
            'inviterId'  => 0, // 新用户
            'inviteCode' => '',
        );

        // 因为线上直接清缓存，so变更
        //$objMemcache = new Hkzb_Ds_Dati_UserMemcache();
        //$userInfo    = $objMemcache->getUserInfo($uid);
        $userInfo = $this->getInfoByUid($uid);
        if (!empty($userInfo)) {
            $ret = [
                'resurgence' => $userInfo['resurgence'] ? $userInfo['resurgence'] : 0,
                'inviterId'  => $userInfo['inviterId'] ? $userInfo['inviterId'] : 0,
                'inviteCode' => $userInfo['inviteCode'] ? $userInfo['inviteCode'] : '',
            ];

        } else {
            $ret['inviteCode'] = $this->getInviteCode($uid);
        }

        return $ret;
    }

    /**
     * 提现接口
     *
     * @param integer $uid   用户id
     * @param integer $money 提现金额
     * @return boolean
     */
    public function draw($uid, $money)
    {
        $uid   = intval($uid);
        $money = intval($money);

        if ($money <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[uid:{$uid}, money:{$money}]");

            return false;
        }

        return $this->modifyUserBalance($uid, $money);
    }

    /**
     * 返还接口
     *
     * @param integer $uid   用户id
     * @param integer $money 返还金额
     * @return boolean
     */
    public function returnMoney($uid, $money)
    {
        $uid   = intval($uid);
        $money = intval($money);

        if ($money <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[uid:{$uid}, money:{$money}]");

            return false;
        }

        return $this->modifyUserBalance($uid, (0 - $money));
    }

    /**
     * 修改用户余额
     *
     * @param integer $uid   用户id
     * @param integer $money 被修改的金额
     * @return bool
     */
    private function modifyUserBalance($uid, $money)
    {
        $uid   = intval($uid);
        $money = intval($money);

        if ($uid <= 0 || $money == 0) {
            Bd_Log::warning("Error:[param error], Detail:[uid:{$uid}, money:{$money}]");

            return false;
        }

        $objDsUser = new Hkzb_Ds_Dati_DatiUser();
        $userInfo  = $objDsUser->getInfoByUidFromDb($uid);

        if (empty($userInfo)) {
            Bd_Log::warning("Error[user info not exist] Detail[uid:$uid]");

            return false;
        }

        $balance    = intval($userInfo['balance']);
        $balanceNew = $balance - intval($money);

        if (intval($balanceNew) < 0) {
            Bd_Log::warning("Error[money is not enough], Detail:[uid:{$uid}, money:{$money}]");

            return false;
        }

        $arrConds = array(
            'uid' => $uid,
        );

        $data = array(
            'balance' => intval($balanceNew),
        );

        $data['updateTime'] = time();

        $objDatiUser = new Hkzb_Dao_Dati_DatiUser();
        if (false === $objDatiUser->updateByConds($uid, $arrConds, $data)) {
            Bd_Log::warning("Error:[update error], Detail:[uid:{$uid}, money:{$money}]");

            return false;
        }

        return $balanceNew;
    }

    /**
     * 获取邀请码 && 同步用户信息表
     *
     * @param  int $uid
     * @return  array
     */
    private function getInviteCode($uid)
    {
        // 邀请码
        $inviteCode = Dati_Util::uidToStr($uid);

        $userInfo = array(
            'uid'        => $uid,
            'resurgence' => 0,
            'inviteCode' => $inviteCode,
            'inviterId'  => 0,
            'totalMoney' => 0,
            'balance'    => 0,
        );

        // 添加用户|复活卡表
        $this->initUserData($userInfo);

        return $inviteCode;
    }

    /**
     * 初始化用户数据
     *
     * @param  array $params
     * @return bool
     */
    public function initUserData($params)
    {
        if (empty($params)) {
            return false;
        }

        // ① 写入memcache - 用户信息
        $objMemcache = new Hkzb_Ds_Dati_UserMemcache();
        $objMemcache->setUserInfo($params['uid'], $params);

        // ② 添加用户|复活卡表
        $this->startTransaction();

        $addUser         = $this->addUser($params);
        $resurgenceInfo  = array(
            'uid'        => $params['uid'],
            'resurgence' => $params['resurgence'],
        );
        $objDsResurgence = new Hkzb_Ds_Dati_DatiResurgence();
        $addResurgence   = $objDsResurgence->addResurgence($resurgenceInfo);
        if (false == $addUser || false == $addResurgence) {
            $infoString = json_encode($params);
            Bd_Log::warning("Error:[add user error], Detail:[$infoString]");
            $this->rollback();

            return false;
        }
        $this->commit();

        return true;
    }

    /**
     * 开启事务
     *
     * @return bool
     */
    public function startTransaction()
    {
        return $this->_objDaoDatiUser->startTransaction();
    }

    /**
     * 回滚
     *
     * @return bool
     */
    public function rollback()
    {
        return $this->_objDaoDatiUser->rollback();
    }

    /**
     * 提交事务
     *
     * @return bool
     */
    public function commit()
    {
        return $this->_objDaoDatiUser->commit();
    }


}
