<?php
/**
 * Created by Growth AutoCode.
 * User: <EMAIL>
 * Date: 2020/03/13
 * Time: 11:32
 */
class Qdlib_Ds_Ad_QudaoAdsMonitor
{
    const ALL_FIELDS = 'id,channel,name,scope,noticeObject,objectiveId,dataCycle,noticeCycle,timeType,noticeTime,status,creator,operator,createTime,updateTime,deleted';
    const DELETE_DEL = 1;

    private $_objDaoQudaoAdsMonitor;

    public function __construct()
    {
        $this->_objDaoQudaoAdsMonitor = new Qdlib_Dao_Ad_QudaoAdsMonitor();
    }

    //新增
    public function addQudaoAdsMonitor($arrParams) {
        $arrFields = array(
            'id'           => isset($arrParams['id']) ? intval($arrParams['id']) : 0,
            'channel'      => isset($arrParams['channel']) ? strval($arrParams['channel']) : '',
            'name'         => isset($arrParams['name']) ? strval($arrParams['name']) : '',
            'scope'        => isset($arrParams['scope']) ? intval($arrParams['scope']) : 0,
            'noticeObject' => isset($arrParams['noticeObject']) ? strval($arrParams['noticeObject']) : '',
            'objectiveId'  => isset($arrParams['objectiveId']) ? intval($arrParams['objectiveId']) : 0,
            'dataCycle'    => isset($arrParams['dataCycle']) ? intval($arrParams['dataCycle']) : 0,
            'noticeCycle'  => isset($arrParams['noticeCycle']) ? strval($arrParams['noticeCycle']) : '',
            'timeType'     => isset($arrParams['timeType']) ? intval($arrParams['timeType']) : 0,
            'noticeTime'   => isset($arrParams['noticeTime']) ? strval($arrParams['noticeTime']) : '',
            'status'       => isset($arrParams['status']) ? intval($arrParams['status']) : 0,
            'creator'      => isset($arrParams['creator']) ? strval($arrParams['creator']) : '',
            'operator'     => isset($arrParams['operator']) ? strval($arrParams['operator']) : '',
            'createTime'   => isset($arrParams['createTime']) ? intval($arrParams['createTime']) : 0,
            'updateTime'   => isset($arrParams['updateTime']) ? intval($arrParams['updateTime']) : 0,
            'deleted'      => isset($arrParams['deleted']) ? intval($arrParams['deleted']) : 0,

        );
        $result = $this->_objDaoQudaoAdsMonitor->insertRecords($arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoAdsMonitor', 'addQudaoAdsMonitor', '数据库插入失败', json_encode($arrFields));
            return false;
        }
        $id = $this->_objDaoQudaoAdsMonitor->getInsertId();
        return $id;
    }

    //编辑
    public function updateQudaoAdsMonitor($arrConds = null, $arrParams) {
        $arrAllFields = explode(',',self::ALL_FIELDS);
        foreach($arrParams as $key => $value){
            if(!in_array($key, $arrAllFields)){
                continue;
            }
            $arrFields[$key] = $value;
        }
        $result = $this->_objDaoQudaoAdsMonitor->updateByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoAdsMonitor', 'updateQudaoAdsMonitor', '数据库更新失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //通过id编辑
    public function updateQudaoAdsMonitorById($id, $arrFields) {
        $arrConds = ['id' => $id];
        return $this->updateQudaoAdsMonitor($arrConds, $arrFields);
    }

    //软删除
    public function deleteQudaoAdsMonitor($arrConds = null)
    {
        $arrFields = ['deleted' => self::DELETE_DEL];
        return $this->updateQudaoAdsMonitor($arrConds, $arrFields);
    }

    //获取信息
    public function getQudaoAdsMonitorInfoById($id, $arrFields = array(), $arrConds = null)
    {
        $arrConds['id'] = $id;

        if (!$arrFields) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $arrConds = ['id' => $id];
        $result = $this->_objDaoQudaoAdsMonitor->getRecordByConds($arrConds, $arrFields);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoAdsMonitor', 'getQudaoAdsMonitorInfoById', '查询数据库信息失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds]));
            return false;
        }
        return $result;
    }

    //获取列表
    public function getQudaoAdsMonitorList($arrConds = null, $arrFields = array(), $arrAppends = null) {
        if (!$arrFields){
            $arrFields = explode(',', self::ALL_FIELDS);
        }
        $result = $this->_objDaoQudaoAdsMonitor->getListByConds($arrConds, $arrFields, null, $arrAppends);
        if ($result === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoAdsMonitor', 'getQudaoAdsMonitorList', '查询数据库列表失败', json_encode(['fields' => $arrFields, 'conds' => $arrConds, 'appends' => $arrAppends]));
            return false;
        }
        return $result;
    }

    //获取总数
    public function getQudaoAdsMonitorTotal($arrConds = null)
    {
        $res = $this->_objDaoQudaoAdsMonitor->getCntByConds($arrConds);
        if ($res === false) {
            Qdlib_Util_Log::warning('Service_Data', 'QudaoAdsMonitor', 'getQudaoAdsMonitorTotal', '查询数据库总数失败', json_encode(['conds' => $arrConds]));
        }
        return $res;
    }
}