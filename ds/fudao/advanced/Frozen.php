<?php
/***************************************************************************
 * 
 * Copyright (c) 2016 zuoyebang.com, Inc. All Rights Reserved
 * 
 **************************************************************************/
 
 
 
/**
 * @file Frozen.php
 * <AUTHOR>
 * @date 2016/9/18 17:13:18
 * @brief 冻结
 *  
 **/

class Hkzb_Ds_Fudao_Advanced_Frozen {
    /*
     * 冻结
     *
     * @param  int  $orderId    订单id
     * @return mix
     */
    public function frozen($orderId, $needTrans = true)
    {
        if (intval($orderId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[orderId:$orderId]");

            return false;
        }

        if ($needTrans) {
            $db  = Hk_Service_Db::getDB('fudao/zyb_fudao');
            $ret = $db->startTransaction();
            if ($ret === false) {
                Bd_Log::warning("Error:[start transaction error]", Hk_Util_ExceptionCodes::DB_ERROR);

                return false;
            }
        }

        $objTradeRecord = new Hkzb_Ds_Fudao_TradeRecord();
        $orderInfo      = $objTradeRecord->getTradeRecord($orderId);
        if (empty($orderInfo)) {
            Bd_Log::warning("Error:[getTradeRecord error], Detail:[orderId:$orderId]");
            if ($needTrans) {
                $db->rollback();
            }

            return false;
        }
        $type              = $orderInfo['type'];
        $studentUid        = $orderInfo['studentUid'];
        $courseId          = $orderInfo['courseId'];
        $objAdvancedCourse = new Hkzb_Ds_Fudao_Advanced_Course();
        $courseInfo        = $objAdvancedCourse->getCourseInfo($courseId);
        if (empty($courseInfo)) {
            Bd_Log::warning("Error:[getCourseInfo error], Detail:[courseId:$courseId]");
            if ($needTrans) {
                $db->rollback();
            }

            return false;
        }
        $subCourseIdList = $objAdvancedCourse->getSubCourseIdList($courseId);
        if (empty($subCourseIdList)) {
            Bd_Log::warning("Error:[getSubCourseIdList error], Detail:[courseId:$courseId]");
            if ($needTrans) {
                $db->rollback();
            }

            return false;
        }
        $objCourse = new Hkzb_Ds_Fudao_Course();
        if ($type == Hkzb_Ds_Fudao_TradeRecord::TYPE_PRE_PAY) {
            //预约订单无需冻结课程,只更新报名人数
            $ret = $objCourse->changeCourseStudentCnt($courseId);
            if ($ret === false) {
                Bd_Log::warning("Error:[changeCourseStudentCnt error], Detail:[courseId:$courseId]");
                if ($needTrans) {
                    $db->rollback();
                }

                return false;
            }
            foreach ($subCourseIdList as $subCourseId) {
                //更新子课报名人数
                $ret = $objCourse->changeCourseStudentCnt($subCourseId);
                if ($ret === false) {
                    Bd_Log::warning("Error:[changeCourseStudentCnt error], Detail:[courseId:$courseId]");
                    if ($needTrans) {
                        $db->rollback();
                    }

                    return false;
                }
            }
        } else {
            $objStudentCourse = new Hkzb_Ds_Fudao_StudentCourse();
            $objTeacherCourse = new Hkzb_Ds_Fudao_TeacherCourse();
            //无未开始的章节，不允许冻结。
            $canFrozen = 0;
            $objLesson = new Hkzb_Ds_Fudao_Lesson();
            foreach ($subCourseIdList as $subCourseId) {
                $lessonList = $objLesson->getLessonListByCourseId($subCourseId, array('status'));
                foreach ($lessonList as $lesson) {
                    if (intval($lesson['status']) === Hkzb_Ds_Fudao_Lesson::STATUS_CLASS_TOSTART) {
                        $canFrozen = 1;
                        break;
                    }
                }
            }
            if (empty($courseInfo)) {
                Bd_Log::warning("Error:[get courseinfo error], Detail:[courseId:$courseId]");
                if ($needTrans) {
                    $db->rollback();
                }

                return false;
            }
            //特殊处理，关课的可以进行冻结
            if ($courseInfo['status'] == Hkzb_Ds_Fudao_Advanced_Course::STATUS_FINISHED && $courseInfo['extData']['stopReason'] == 'closecourse') {
                $canFrozen = 1;
            }
            if ($canFrozen === 0) {
                Bd_Log::warning("Error:[no subcourse can frozen], Detail:[orderId:$orderId]");
                if ($needTrans) {
                    $db->rollback();
                }

                return false;
            }

            foreach ($subCourseIdList as $subCourseId) {
                //子课冻结
                $studentCourseInfo = $objStudentCourse->getStudentCourseInfo($studentUid, $subCourseId);
                //更新辅导老师报名人数
                $assistantUid = intval($studentCourseInfo['assistantUid']);
                $classId      = intval($studentCourseInfo['classId']);
                $objTeacherCourse->changeTeacherStudentCnt($subCourseId, $assistantUid, $classId);
                //更新子课报名人数
                $objCourse->changeCourseStudentCnt($subCourseId);
                //子课冻结
                $extData         = $studentCourseInfo['extData'];
                $payOrderList    = isset($extData['payOrderList']) ? $extData['payOrderList'] : array();
                $refundOrderList = isset($extData['refundOrderList']) ? $extData['refundOrderList'] : array();
                $newPayOrderList = array();
                foreach ($payOrderList as $payOrderId) {
                    if ($payOrderId == $orderId) {
                        $refundOrderList[] = $payOrderId;
                    } else {
                        $newPayOrderList[] = $payOrderId;
                    }
                }
                $extData['payOrderList']    = $newPayOrderList;
                $extData['refundOrderList'] = $refundOrderList;
                //子课没有其它支付的订单，则冻结
                if (empty($newPayOrderList)) {
                    $extData['deleteTime']   = time();
                    $extData['deleteReason'] = '退款';
                    //冻结课程
                    $ret = $objStudentCourse->deleteStudentCourse($studentUid, $subCourseId);
                    if (false === $ret) {
                        Bd_Log::warning("Error:[db error], Detail:[studentUid:$studentUid subCourseId:$subCourseId]");
                        if ($needTrans) {
                            $db->rollback();
                        }

                        return false;
                    }
                }
                //更新扩展信息
                $ret = $objStudentCourse->setExtData($studentUid, $subCourseId, $extData);
                if (false === $ret) {
                    Bd_Log::warning("Error:[db error], Detail:[studentUid:$studentUid subCourseId:$subCourseId]");
                    if ($needTrans) {
                        $db->rollback();
                    }

                    return false;
                }
            }
            //更新打包课报名人数
            if ($courseInfo['pack'] == Hkzb_Ds_Fudao_Course::PACK_YES) {
                $objCourse->changeCourseStudentCnt($courseId);
                //更新用户报名记录
                $objAdvancedStudentCourse = new Hkzb_Ds_Fudao_Advanced_StudentCourse();
                $studentCourseInfo        = $objAdvancedStudentCourse->getStudentCourseInfo($studentUid, $courseId);
                $extData                  = $studentCourseInfo['extData'];
                $payOrderList             = isset($extData['payOrderList']) ? $extData['payOrderList'] : array();
                $refundOrderList          = isset($extData['refundOrderList']) ? $extData['refundOrderList'] : array();
                $newPayOrderList          = array();
                foreach ($payOrderList as $payOrderId) {
                    if ($payOrderId == $orderId) {
                        $refundOrderList[] = $payOrderId;
                    } else {
                        $newPayOrderList[] = $payOrderId;
                    }
                }
                $extData['payOrderList']    = $newPayOrderList;
                $extData['refundOrderList'] = $refundOrderList;
                //子课没有其它支付的订单，则冻结
                if (empty($newPayOrderList)) {
                    $extData['deleteTime']   = time();
                    $extData['deleteReason'] = '退款';
                    //冻结课程
                    $ret = $objAdvancedStudentCourse->deleteStudentCourse($studentUid, $courseId);
                    if (false === $ret) {
                        Bd_Log::warning("Error:[db error], Detail:[studentUid:$studentUid subCourseId:$subCourseId]");
                        if ($needTrans) {
                            $db->rollback();
                        }

                        return false;
                    }
                }
                //更新扩展信息
                $ret = $objAdvancedStudentCourse->setExtData($studentUid, $courseId, $extData);
                if (false === $ret) {
                    Bd_Log::warning("Error:[db error], Detail:[studentUid:$studentUid courseId:$courseId]");
                    if ($needTrans) {
                        $db->rollback();
                    }

                    return false;
                }
            }
        }
        if ($needTrans) {
            $ret = $db->commit();
            if ($ret === false) {
                Bd_Log::warning("Error:[db commit error]");

                return false;
            }
        }

        return true;
    }

    /**  
     * 退课成功通知班主任工作站
     * @param $arrData
     * @return true/false
     */
    public function _delAssistantMis($courseId,$studentUid){
        $data = array();
        $data['courseId']   = $courseId;
        $data['studentUid'] = $studentUid;
        $data['time']         = time();
        $objNmq            = new Hk_Service_Nmq();
        $ret               = $objNmq->talkToQcm(Hk_Const_Command::CMD_FUDAO_DEL_STUDENT_ASSISTANT, $data);
        if(false === $ret){
            Bd_Log::warning("Error:[servies nmq error], Detail:[".json_encode($data)."]");
        }
        return $ret;
    }
} 
