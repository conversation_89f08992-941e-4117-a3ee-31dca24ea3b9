<?php
/**
 * @file ModelInfo.php.
 * <AUTHOR>
 * @date: 2017/11/17
 */
class Hkzb_Ds_Fudao_Admin_ModelInfo
{


    const ALL_FIELDS = "modelId,menuSort,menuTitle,menuUrl,menuType,menuPrivileges,deleted,sysType";

    private $_objDaoModelInfo;

    public function __construct()
    {
        $this->_objDaoModelInfo = new Hkzb_Dao_Fudao_Admin_ModelInfo();
    }









    /**
     * @param $arrConds
     * @param array $arrFields
     * @param $offset
     * @param $limit
     * @return array|false
     */
    public function getModelInfoListByConds($arrConds, $arrFields = array(), $offset, $limit){
        if(empty($arrFields)) {
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        $arrAppends = array(
            "order by modelId desc",
            "limit $offset, $limit",
        );

        $ret = $this->_objDaoModelInfo->getListByConds($arrConds, $arrFields, NULL, $arrAppends);

        return $ret;
    }
}