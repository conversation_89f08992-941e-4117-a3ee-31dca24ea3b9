<?php

/**
 * @file   Class StudentNotice.php
 * <AUTHOR>
 * @date   2017/09/21 19:33:21
 * @brief
 **/
class Hkzb_Ds_Fudao_StudentNotice
{

    // 数据库字段
    const ALL_FIELDS = 'id,action,lessonId,studentId,source,vcname,device,cost,pullAddress,pullIp,studentIp,network,clientTime,extData,createTime';

    private $objDaoStudentNotice;

    public function __construct()
    {
        $this->objDaoStudentNotice = new Hkzb_Dao_Fudao_StudentNotice();
    }

    //添加数据
    public function add($arrParams)
    {

        $intLessonId = intval($arrParams['lessonId']);
        $arrFields = array(
            'action'      => intval($arrParams['action']),
            'lessonId'    => $intLessonId,
            'studentId'   => intval($arrParams['studentId']),
            'source'      => strval($arrParams['source']),
            'vcname'      => strval($arrParams['vcname']),
            'device'      => strval($arrParams['device']),
            'cost'        => intval($arrParams['cost']),
            'pullAddress' => strval($arrParams['pullAddress']),
            'pullIp'      => strval($arrParams['pullIp']),
            'studentIp'   => strval($arrParams['studentIp']),
            'network'     => strval($arrParams['network']),
            'clientTime'  => intval($arrParams['clientTime']),
            'createTime'  => time(),
        );
        if(!empty($arrParams['extData'])){
            $arrFields['extData'] = json_encode(json_decode($arrParams['extData']));
        }

        return $this->objDaoStudentNotice->insertRecords($intLessonId, $arrFields);
    }

    // 根据条件获取指定条数
    public function getLogListByConds($intLessonId, $arrConds, $arrFields){
        // 检验查询参数
        if(empty($arrConds)){
            Bd_Log::warning('Error:[param error], Detail:['.json_encode($arrConds).']');
            return false;
        }

        // 检验查询参数
        if(empty($arrFields)){
            $arrFields = explode(',', self::ALL_FIELDS);
        }

        // 返回查询结果
        return $this->objDaoStudentNotice->getListByConds($intLessonId, $arrConds, $arrFields,null);
    }
}