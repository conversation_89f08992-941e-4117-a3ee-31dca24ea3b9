<?php
/**
 * @file ZbTikuTask.php.
 * <AUTHOR>
 * @date: 2018/07/02
 */
class Hkzb_Ds_TikuTar_ZbTikuTask
{
    private $_objDaoZbTikuTask;
    const RAL_NAME = "platmis";    //集群ral配置名字
    const TIKU_API = "/misauth/api/";     //题库操作接口

    public function __construct()
    {
        $this->_objDaoZbTikuTask = new Hkzb_Dao_TikuTar_ZbTikuTask();
    }

    /**
     * @param $addInfo
     * @param $userInfo
     * @return int|bool
     */
    public function addZbTikuTask($addInfo,$userInfo)
    {
        $now = time();
        $addData = array(
            'taskName'         => $addInfo['taskName'],
            'belongProjectId'  => $addInfo['belongProjectId'],
            'fileName'         => $addInfo['fileName'],
            'fileNamePid'      => $addInfo['fileNamePid'],
            'executorUid'      => $addInfo['executorUid'],
            'executor'         => $addInfo['executor'],
            'priority'         => $addInfo['priority'],
            'homeworkNum'      => $addInfo['homeworkNum'],
            'remark'           => $addInfo['remark'],
            'status'           => 0,
            'deleted'          => 0,
            'createTime'       => $now,
            'createUid'        => $userInfo['uid'],
            'createName'       => $userInfo['nickName'],
            'updateTime'       => $now,
        );
        $ret = $this->_objDaoZbTikuTask->insertRecords($addData);
        if( false === $ret ) {
            Bd_Log::warning(" insert TikuTask error detail:".json_encode($addData));
            return false;
        }

        $taskId = $this->_objDaoZbTikuTask->getInsertId();

        return $taskId;
    }


    /**
     * @param $taskId
     * @param $arrParams
     * @return bool
     */
    public function updateZbTikuTask($taskId, $arrParams)
    {
        if(intval($taskId) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[task_id:$taskId]");
            return false;
        }

        $arrCond = array(
            'task_id' => intval($taskId),
        );

        $arrFields = array();
        $arrAllFields = $this->_objDaoZbTikuTask->arrFields;
        foreach ($arrParams as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }
            $arrFields[$key] = $value;
        }

        if (empty($arrFields['updateTime'])){
            $arrFields['updateTime'] = time();
        }

        $ret = $this->_objDaoZbTikuTask->updateByConds($arrCond, $arrFields);

        return $ret;
    }

    /**
     * @param $arrConds
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return array|false
     */
    public function getZbTikuTaskListByConds($arrConds, $arrFields = array(), $offset = 0, $limit = 0)
    {
        $arrFields = empty($arrFields) ? $this->_objDaoZbTikuTask->arrFields : $arrFields;
        $arrAppends = null;
        $arrAppends = array();
        $arrAppends[] = "order by belong_project_id desc,field(priority,1,2,3,4,0),field(status,0,2,1),create_time desc";
        if ($limit > 0){
            $arrAppends[] = 'limit ' . $offset . ',' . $limit;
        }
        $arrConds['deleted'] = 0;
        $ret = $this->_objDaoZbTikuTask->getListByConds($arrConds, $arrFields, null, $arrAppends);

        return $ret;
    }


    /**
     * 根据主键查询任务信息
     * @param  int $taskId     任务id
     * @param  array  $arrFields 列
     * @return array            结果集
     */
    public function getZbTikuTaskInfoByTaskId($taskId,$arrFields=array())
    {
        if(empty($taskId)) {
            return array();
        }
        $arrFields = empty($arrFields) ? $this->_objDaoZbTikuTask->arrFields : $arrFields;
        $arrConds = array(
            'taskId'   => $taskId,
            'deleted' => 0,
        );
        $return = $this->_objDaoZbTikuTask->getRecordByConds($arrConds,$arrFields);
        if( false === $return ) {
            $return = array(); 
        }
        return $return;
    }
    
    /**
     * 
     * @param array $arrConds
     * @return type
     */
    public function getZbTikuTaskCnt($arrConds) {
        $arrConds['deleted'] = 0;
        $returnTmp = 0;
        $returnTmp += $this->_objDaoZbTikuTask->getCntByConds($arrConds);
        return $returnTmp;
    }
    

    
    /**
     * 获取用户信息
     * @param array $arrConds
     * @return bool | array
     */
    public function fetchUserList($arrConds = array()){
        $arrConds['platform'] = array('zbtiku');
        $header['pathinfo'] = self::TIKU_API."getuserbycondition";
        $retJson = ral(self::RAL_NAME, 'post', $arrConds, rand(), $header);
        $retArr = json_decode($retJson,true);
        if(empty($retArr['data'])) {
            Bd_log::warning("Error[insert questinfo by ral] detail ".json_encode($header)." data ".json_encode($arrConds));
            return false;
        }
        return $retArr['data'];
    }
}