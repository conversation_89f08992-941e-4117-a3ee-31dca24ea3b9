<?php
/**
 * @file TmpExamMap.php.
 * <AUTHOR>
 * @date: 2018/6/4
 */
class Hkzb_Ds_TikuTar_TmpExamMap
{
    private $_objDaoTmpExamMap;
    private $_userInfo;
    private $_ObjDaoExamPaperStudent;
    static $courseUseType = array(
        3, //报前测
        4, //报后测
    );

    public function __construct($userInfo=array())
    {
        $this->_objDaoTmpExamMap = new Hkzb_Dao_TikuTar_TmpExamMap();
        $this->_ObjDaoExamPaperStudent = new Hkzb_Dao_Fudao_Exam_ExamPaperStudent();
        $this->_userInfo         = $userInfo;
    }

    /**
     * @param $examId int 试卷id
     * @param $tmpId  int 模板id
     * @param $bindId int 课程or章节Id
     * @param $bindType int 模板类型
     * @param $addParams
     * @return array|bool
     */
    public function addTmpExamMap($examId,$tmpId,$bindId,$bindType,$addParams)
    {
        if (intval($examId) <= 0 || intval($tmpId) <= 0 || intval($bindId) <= 0 || intval($bindType) <= 0){
            Bd_Log::warning("param error Detail[examId:$examId,tmpId:$tmpId,bindId:$bindId,bindType:$bindType]");
            return false;
        }

        $addData = array(
            'examId'     => intval($examId),
            'tmpId'      => intval($tmpId),
            'bindId'     => intval($bindId),
            'bindType'   => intval($bindType),
            'flag'       => empty($addParams['flag']) ? 0 : intval($addParams['flag']),
            'ext'        => empty($addParams['ext']) ? '' : json_encode($addParams['ext'], JSON_UNESCAPED_UNICODE),
            'deleted'    => 0,
            'createTime' => time(),
            'updateTime' => time(),
            'createUid'  => empty($addParams['createUid']) ? 0 : intval($addParams['createUid']),
            'createName' => empty($addParams['createName']) ? '' : strval($addParams['createName']),
        );

        $ret = $this->_objDaoTmpExamMap->insertRecords($addData);
        if( false === $ret ) {
            Bd_Log::warning("tmpExamMap insert error detail:".json_encode($addData));
            return false;
        }

        $id = $this->_objDaoTmpExamMap->getInsertId();
        return array('id'=>$id);
    }

    /**
     * @param $id
     * @param $updateInfo
     * @return bool
     */
    public function updateTmpExamMap($id, $updateInfo)
    {
        if(intval($id) <= 0) {
            Bd_Log::warning("Error:[param error], Detail:[id:$id]");
            return false;
        }

        $arrCond = array(
            'id' => intval($id),
        );

        $arrFields = array();
        $arrAllFields = $this->_objDaoTmpExamMap->arrFields;
        foreach ($updateInfo as $key => $value) {
            if (!in_array($key, $arrAllFields)) {
                continue;
            }
            $arrFields[$key] = $value;
        }

        //ext由ps补全，json处理在ds
        if (isset($arrFields['ext'])) {
            $arrFields['ext'] = json_encode($arrFields['ext'], JSON_UNESCAPED_UNICODE);
        }

        if (empty($arrFields['updateTime'])){
            $arrFields['updateTime'] = time();
        }

        $ret = $this->_objDaoTmpExamMap->updateByConds($arrCond, $arrFields);

        return $ret;
    }

    /**
     * @param $arrConds
     * @param array $arrFields
     * @param int $offset
     * @param int $limit
     * @return array|false
     */
    public function getTmpExamListByConds($arrConds, $arrFields = array(), $offset=0, $limit=0)
    {
        $arrFields = empty($arrFields) ? $this->_objDaoTmpExamMap->arrFields : $arrFields;

        $arrAppends = null;
        if ($limit > 0){
            $arrAppends = array();
            $arrAppends[] = 'limit ' . $offset . ',' . $limit;
        }

        $ret = $this->_objDaoTmpExamMap->getListByConds($arrConds, $arrFields, null, $arrAppends);

        return $ret;
    }

    /**
     * @param $tmpId int 模板id
     * @param $bindType int 试卷类型
     * @param $addParams
     * @return array|bool
     */
    public function bindTmpExamMap($tmpId,$bindType,$addParams)
    {
        if (intval($tmpId) <= 0 || intval($bindType) <= 0 || empty($addParams['bindInfo'])){
            Bd_Log::warning("param error Detail[tmpId:{$tmpId},bindType:{$bindType}, bindInfo: ".json_encode($addParams['bindInfo'])."]");
            return false;
        }

        //绑定
        foreach ($addParams['bindInfo'] as $key=>$bindInfo){
            $ret = $this->addTmpExamMap($bindInfo['examId'],$tmpId,$bindInfo['bindId'],$bindType,$addParams);
            if (!$ret){
                Bd_Log::warning("bind error Detail[examId:".$bindInfo['examId'].",bindId:".$bindInfo['bindId']. "]");
                return false;
            }
        }
        //回写试题
        $ret = (new Hkzb_Ds_TikuTar_ExamPaperTemplate())->updateExamPaperTemplate($tmpId, array('status'=>Hkzb_Ds_TikuTar_ExamPaperTemplate::STATUS_BIND));
        if (false === $ret){
            Bd_Log::warning("update examPaperTemplate");
            return false;
        }

        return $ret;
    }
    
    public function fetchCourseLessonList($arrTmpExamMapList){
        if(!is_array($arrTmpExamMapList)){
            return array();
        }
        $ArrCourseLesson = array();
        foreach($arrTmpExamMapList as $key => $value){
            $courseLessonParam = array();
            $courseLessonParam[$key]['id'] = $value['bind_id'];
            if(in_array($value['bind_type'], Hkzb_Ds_TikuTar_TmpExamMap::$courseUseType)){
                $courseLessonParam[$key]['type'] = 1;
            }else{
                $courseLessonParam[$key]['type'] = 2;
            }
            $courseLessonInfo = $this->getCourseLessonList($courseLessonParam);
            if(!empty($courseLessonInfo)){
                $courseLessonInfo[0]['examId'] = $value['exam_id'];
                $ArrCourseLesson[] = $courseLessonInfo[0];
            }
        }
        return $ArrCourseLesson;
    }
    
    public function getCourseLessonList($param){
//        $param = array(
//            array(
//                'id'=>115961,
//                'type'=>2
//            ),
//            array(
//                'id'=>115960,
//                'type'=>2
//            ),
//            array(
//                'id'=>115963,
//                'type'=>2
//            ),
//            array(
//                'id'=>115962,
//                'type'=>2
//            ),
//            array(
//                'id'=>115964,
//                'type'=>2
//            ),
//            array(
//                'id'=>115959,
//                'type'=>2,
//            )
//        );
        $header['pathinfo'] = Hkzb_Ds_TikuTar_ExamPaperTemplate::MISCOURSE_API."courselessonlist";
        $arrConds = array();
        $arrConds['courseLessonIdList'] = $param;
        $arrConds['sid'] = Hkzb_Ds_TikuTar_ExamPaperTemplate::MISCOURSE_KEY;
        $courseLessonListJson = ral(Hkzb_Ds_TikuTar_ExamPaperTemplate::RAL_NAME, 'POST', $arrConds, rand(), $header);
        $courseLessonList = json_decode($courseLessonListJson,true);
        if(empty($courseLessonList['data'])) {
            Bd_log::warning("Error[get courselessonlist by ral] detail：".json_encode($header));
            return array();
        }
        $courseLessonData = $courseLessonList['data'];
        return $courseLessonData;
    }

    /**
     * 格式化时间格式 
     * @param  string &$durationTxt 时间"2019-01-05 16:00:00 2019-01-05 17:30:00"
}
     * @return array            开始时间，结束时间
     */
    public function farmatDurationTxt($durationTxt)
    {   
        if(!$durationTxt) {
            Bd_log::warning("Error[Hkzb_Ds_TikuTar_TmpExamMap->_farmatDurationTxt param fail]");
            return false;
        }
        $arrDurationTime = array();
        $arrDurationTmp = explode(" ", $durationTxt);
        if(count($arrDurationTmp)<4) {
            Bd_log::warning("Error[Hkzb_Ds_TikuTar_TmpExamMap->_farmatDurationTxt arrDurationTmp fail detail durationTxt:$durationTxt]");
            return false;
        }
        $arrDurationTime['startTime'] = strtotime($arrDurationTmp[0]." ".$arrDurationTmp[1]);
        $arrDurationTime['endTime']   = strtotime($arrDurationTmp[2]." ".$arrDurationTmp[3]);
        return $arrDurationTime;
    }
    
    
    public function getTmpExamMapCnt($arrConds) {
        $arrConds['deleted'] = 0;
        $returnTmp = $this->_objDaoTmpExamMap->getCntByConds($arrConds);
        return $returnTmp;
    }
    
    public function updateTmpExamMapByConds($arrConds,$updateInfo){
        $arrAllFields = $this->_objDaoTmpExamMap->arrFields;
        $ret = $this->_objDaoTmpExamMap->updateByConds($arrConds, $updateInfo);
        return $ret;
    }

    /**
     * 根据直播tid获取试卷idList
     * @param  int $zbTid 直播tid
     * @return array        
     */
    public function getExamInfoListByZbTid($zbTid)
    {
        $arrTidMap = array(
            "zbTid"   => $zbTid,
            "deleted" => 0,
        );
        /** 获取tmpId */
        $arrTmpIdListData = (new Hkzb_Ds_TikuTar_ExamTidMap())->getExamTidMapByConds($arrTidMap,array("tmpId"));
        if(empty($arrTmpIdListData)) {
            Bd_log::warning("Error[Hkzb_Ds_TikuTar_ExamTidMap is empty detail：zbTid[$zbTid]]");
            return array();
        }
        $arrTmpId = array();
        foreach ($arrTmpIdListData as $key => $tmpInfo) {
            $arrTmpId[] = $tmpInfo['tmpId'];
        }
        $arrExamTmplateConds[] = " tmp_id in (".implode(",", $arrTmpId).")";
        $arrExamTmplateConds[] = " deleted=0";
        $arrExamTmplateInfo = $this->getTmpExamListByConds($arrExamTmplateConds,array("examId","tmpId","bindId","bindType"));
        if(empty($arrExamTmplateInfo)) {
            Bd_log::warning("Error[Hkzb_Ds_TikuTar_ExamTidMap is empty detail：zbTid[".json_encode($arrTmpId)."]]");
            return array();
        }
        $answerInfo = $this->getStudentAnswer($arrExamTmplateInfo);
        return $answerInfo;
    }

    /**
     * 获取学生作答信息（临时使用）
     * @param  int $arrExamIdList   试卷Id
     * @param  int $bindType 试卷类型
     * @return array           
     */
    public function getStudentAnswer($arrBindInfoList)
    {
        /**
         * 堂堂测查询tblExamAnswer0
         * 其它测试查询tblExamPaperStudentX
         */
        //获取examId试卷ID
        $objExamPaperStudent = new Hkzb_Ds_Fudao_Exam_ExamPaperStudent();
        $arrStudentUid = array();
        //读取学生答题情况
        foreach ($arrBindInfoList as $bindInfo) {
           if($bindInfo['bindType']==10){
                $arrStudentList = $this->_getAnswerInclassExam($bindInfo);
           }else{
                $arrStudentList = $this->_getAnswerExam($bindInfo);
           }
           if($arrStudentList)
            $arrStudentUid[$bindInfo["examId"]] = $arrStudentList;
        }
        return $arrStudentUid;
    }

    private function _getAnswerExam($bindInfo)
    {
        $arrConds["examId"]       = $bindInfo["examId"];
        $arrConds["relationType"] = $bindInfo["bindType"];
        $arrFields = array("studentUid");
        $examPaperStudentInfo = $this->_ObjDaoExamPaperStudent->getListByConds($bindInfo['examId'],$arrConds,$arrFields);
        return $examPaperStudentInfo;
    }
    private function _getAnswerInclassExam($bindInfo)
    {
        $sql = "select uid studentUid from tblExamAnswer0 where exam_id={$bindInfo['examId']}";
        $examPaperStudentInfo = $this->_ObjDaoExamPaperStudent->query($sql);
        return $examPaperStudentInfo;
    }

}