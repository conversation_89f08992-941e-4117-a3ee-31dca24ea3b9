<?php

/**
 * @file    AfterSale.php
 * <AUTHOR> <<EMAIL>>
 * @date    2018/5/19 16:53
 * @brief   客服后台 - 售后
 *
 * */
class Hkzb_Ds_Gnmis_AfterSale
{
    const ALL_FIELDS = 'id,aftersalePid,orderId,orderUid,courseId,kind,type,applyReason,applyDetail,createUser,createTime,createIdentity,lastOpUser,lastOpTime,nextOpRole,nextStep,flows,expressBackId,expressId,refundId,status,deleted,abstract,extData,sign';

    // 售后类型
    const ORDER_REFUND                 = 0;  //退款
    const ORDER_CHANGE_GIFT            = 1;  //换货
    const ORDER_EXPRESS_URGENT         = 2;  //快递加急
    const ORDER_UPDATE_EXPRESS_ADDRESS = 3;  //修改收货地址
    const ORDER_FOLLOW_UP              = 4;  //申请补寄
    static $afterSaleTypeMap = [
        self::ORDER_REFUND                 => '退款',
        self::ORDER_CHANGE_GIFT            => '换货',
        self::ORDER_EXPRESS_URGENT         => '快递加急',
        self::ORDER_UPDATE_EXPRESS_ADDRESS => '修改收货地址',
        self::ORDER_FOLLOW_UP              => '申请补寄',
    ];

    // 售后状态
    const STATUS_WAIT   = 0;
    const STATUS_START  = 1;
    const STATUS_END    = 2;
    const STATUS_REFUSE = 3;
    static $afterSaleStatusMap = [
        self::STATUS_WAIT   => '未开始',
        self::STATUS_START  => '处理中',
        self::STATUS_END    => '处理完毕',
        self::STATUS_REFUSE => '已拒绝',
    ];

    // 售后流对应操作名称
    const OPERATION_INIT            = 'init';
    const OPERATION_INPUT_NUMBER    = 'inputNumber';
    const OPERATION_WAREHOUSE_SIGN  = 'warehouseSign';
    const OPERATION_WAREHOUSE_SEND  = 'warehouseSend';
    const OPERATION_WAREHOUSE_EDIT  = 'warehouseEdit';
    const OPERATION_EDIT_ADDRESS    = 'editAddress';
    const OPERATION_USER_SEND       = 'userSend';
    const OPERATION_FINANCIAL_AUDIT = 'financialAudit';
    const OPERATION_REFUNDS_RESULT  = 'refundsResult';
    static $operationNameMap = [
        self::OPERATION_INIT            => '提交申请',
        self::OPERATION_INPUT_NUMBER    => '录入快递单号',
        self::OPERATION_WAREHOUSE_SIGN  => '签收快递',
        self::OPERATION_WAREHOUSE_SEND  => '寄送快递',
        self::OPERATION_WAREHOUSE_EDIT  => '快递加急',
        self::OPERATION_EDIT_ADDRESS    => '变更收货地址',
        self::OPERATION_USER_SEND       => '签收',
        self::OPERATION_FINANCIAL_AUDIT => '审核',
        self::OPERATION_REFUNDS_RESULT  => '退款',
    ];

    private $objDaoAfterSale;

    public function __construct()
    {
        $this->objDaoAfterSale = new Hkzb_Dao_Gnmis_AfterSale();
    }

    /**
     * 更新售后退款物流信息 - 根据refundId
     *
     * @datetime 2018/5/19 17:17
     * <AUTHOR> <<EMAIL>>
     *
     * @param $refundId      int 退款id
     * @param $expressNumber string 物流单号
     * @param $express       string 物流公司
     *
     * @return bool
     */
    public function updateAfterExpressInfoByRefundId($refundId, $expressNumber, $express)
    {
        if (intval($refundId) <= 0 || empty($expressNumber) === '' || empty($express) === '') {
            Bd_Log::warning("[error] param error [Detail:expressNumber - {$$expressNumber} express - {$express} refundId-" . intval($refundId) . "]");

            return false;
        }

        $arrConds = [
            'refundId' => $refundId,
            'deleted'  => 0,
        ];

        $afterSaleInfo = $this->objDaoAfterSale->getRecordByConds($arrConds, ['applyDetail']);

        if (!$afterSaleInfo) {
            Bd_Log::warning("[error] select afterSale Info error [Detail:refundId-{$refundId}]");

            return false;
        }

        $applyDetail                                   = $afterSaleInfo['applyDetail'];
        $applyDetail['returnGift'][0]['expressNumber'] = $expressNumber;
        $applyDetail['returnGift'][0]['express']       = $express;

        $ret = $this->objDaoAfterSale->updateByConds($arrConds, ['applyDetail' => json_encode($applyDetail)]);

        return $ret;
    }

    /**
     * 更新售后退款物流信息 - 根据conds
     *
     * @datetime 2018/5/19 17:17
     * <AUTHOR> <<EMAIL>>
     *
     * @param $arrConds       array 筛选条件
     * @param $arrFields      array 字段信息
     *
     * @return bool
     */
    public function updateAfterExpressInfoByConds($arrConds, $arrFields)
    {
        $arrConds['deleted'] = 0;

        $afterSaleInfo = $this->objDaoAfterSale->getRecordByConds($arrConds, ['id', 'applyDetail', 'nextStep', 'flows']);

        if (!$afterSaleInfo) {
            Bd_Log::warning("[error] select afterSale Info error");

            return false;
        }

        // 获取当前操作退款赠品操作流步骤
        // 对比真正操作流步骤，用以验证是否为先签收快递-后更新快递数据
        $nowNextStep = $afterSaleInfo['nextStep'];
        $flows       = $afterSaleInfo['flows'];
        $nowFlowName = $flows[$nowNextStep - 1]['name'];

        if ($nowFlowName != self::OPERATION_FINANCIAL_AUDIT) { //非财务审核步骤，即可更新下一步操作数据
            $nextStep   = $afterSaleInfo['nextStep'] + 1;
            $flows      = $afterSaleInfo['flows'];
            $nextOpRole = isset($flows[$nowNextStep]['role']) ? $flows[$nowNextStep]['role'] : 0;
        }

        $expressNumber = $arrFields['express_number'];
        $express       = $arrFields['express'];

        $applyDetail                                   = $afterSaleInfo['applyDetail'];
        $applyDetail['returnGift'][0]['expressNumber'] = $expressNumber;
        $applyDetail['returnGift'][0]['express']       = $express;

        $updateInfo = [
            'applyDetail' => json_encode($applyDetail),
        ];
        if (isset($nextStep)) {
            $updateInfo['nextStep'] = $nextStep;
            $updateInfo['nextOpRole'] = $nextOpRole;
        }

        $ret = $this->objDaoAfterSale->updateByConds(
            ['id' => $afterSaleInfo['id']],
            $updateInfo
        );
        if (!$ret) {
            return false;
        }

        return true;
    }

    /**
     * 获取售后详情 - 通过arrConds
     *
     * @datetime 2018/5/21 11:17
     * <AUTHOR> <<EMAIL>>
     *
     * @param   int      $afterSaleId 售后id
     * @param null|array $arrFields   字段信息
     *
     * @return array|bool|false
     */
    public function getAfterSaleInfoByConds($arrConds, $arrFields = null)
    {
        $arrConds['deleted'] = 0;

        if (empty($arrFields)) {
            $arrFields = explode(",", self::ALL_FIELDS);
        }

        // 获取售后信息
        $afterSaleInfo = $this->objDaoAfterSale->getRecordByConds($arrConds, $arrFields);
        if (!$afterSaleInfo) {
            Bd_Log::warning("[error] select afterSaleInfo is error [Detail:conds-" . json_encode($arrConds) . "]");

            return false;
        }

        return $afterSaleInfo;
    }

    /**
     * 获取售后详情 - 通过id
     *
     * @datetime 2018/5/21 11:17
     * <AUTHOR> <<EMAIL>>
     *
     * @param   int      $afterSaleId 售后id
     * @param null|array $arrFields   字段信息
     *
     * @return array|bool|false
     */
    public function getAfterSaleInfoById($afterSaleId, $arrFields = null)
    {
        if (intval($afterSaleId) <= 0) {
            Bd_Log::warning("[error] param is error [Detail:afterSaleId-{$afterSaleId}]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(",", self::ALL_FIELDS);
        }

        // 获取售后信息
        $afterSaleInfo = $this->objDaoAfterSale->getRecordByConds(['id' => $afterSaleId, 'deleted' => 0], $arrFields);
        if (!$afterSaleInfo) {
            Bd_Log::warning("[error] select afterSaleInfo is error [Detail:afterSaleId-{$afterSaleId}]");

            return false;
        }

        return $afterSaleInfo;
    }

    /**
     * 判断售后单完成度 - 多用于子售后单
     *
     * @datetime 2018/5/21 11:25
     * <AUTHOR> <<EMAIL>>
     *
     * @param  array $afterSaleIds 子售后单ids
     * @param null   $arrsFields   字段信息
     *
     * @return bool
     */
    public function judgeAfterSaleComplete($afterSaleIds, $arrsFields = null)
    {
        if (empty($afterSaleIds) == '' || !is_array($afterSaleIds)) {
            Bd_Log::warning("[error] param is error [Detail:afterSaleIds-" . json_encode($afterSaleIds) . "]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(",", self::ALL_FIELDS);
        }

        $completeNum = 0; // 默认子售后完成数 为 0
        // 循环获取子售后单完成状态
        foreach ($afterSaleIds as $id) {
            $afterSaleInfo = $this->objDaoAfterSale->getRecordByConds(['id' => $id, 'deleted' => 0], $arrsFields);
            if ($afterSaleInfo && $afterSaleInfo['status'] === self::STATUS_END) {
                $completeNum += 1;
            }
        }

        if ($completeNum === count($afterSaleIds)) { //售后单全部处理完毕
            return true;
        }

        return false;
    }

    /**
     * 获取多个售后详情 - 通过ids
     *
     * @datetime 2018/5/21 12:00
     * <AUTHOR> <<EMAIL>>
     *
     * @param   int      $afterSaleId   售后id
     * @param null|array $arrFields     字段信息
     * @param  bool      $updateExpress 是否只更新赠品信息
     *
     * @return array|bool|false
     */
    public function getArrAfterSale($afterSaleIds, $updateExpress = false, $arrFields = null)
    {
        if (!is_array($afterSaleIds) || empty($afterSaleIds)) {
            Bd_Log::warning("[error] param is error [Detail:afterSaleIds-" . json_encode($afterSaleIds) . "]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(",", self::ALL_FIELDS);
        }

        $ret = [];

        foreach ($afterSaleIds as $val) {
            if (!$updateExpress) {
                $arrConds = ['id' => $val, 'deleted' => 0, 'sign != 1'];
            } else {
                $arrConds = ['id' => $val, 'deleted' => 0];
            }
            $afterSaleInfo = $this->objDaoAfterSale->getRecordByConds($arrConds, $arrFields);
            if ($afterSaleInfo) {
                $ret[$val] = $afterSaleInfo;
            }
        }

        return $ret;
    }

    // 通过id查询信息
    public function getInfoById($id, $arrFields = [])
    {
        if (intval($id) <= 0) {
            Bd_Log::warning("[error] param error [Detail:id - {$id} ]");

            return false;
        }

        if ($arrFields == array()){
            $arrFields = Hkzb_Dao_Gnmis_AfterSale::$allFields;
        }
        $afterSaleInfo = $this->objDaoAfterSale->getRecordByConds(['id' => $id, 'deleted' => 0], $arrFields);

        return $afterSaleInfo;
    }

    // 通过pid和giftId查询信息
    public function getInfoByPidAndGiftId($pid, $giftId, $nextStep, $arrFields = [])
    {
        if (intval($pid) <= 0 || intval($giftId) <= 0 || intval($nextStep) <= 0) {
            Bd_Log::warning("[error] param error [Detail:pid - {$pid} giftId - {$giftId} nextStep - {$nextStep} ]");

            return false;
        }

        if ($arrFields == array()){
            $arrFields = Hkzb_Dao_Gnmis_AfterSale::$allFields;
        }
        $afterSaleInfo = $this->objDaoAfterSale->getRecordByConds(['aftersalePid' => $pid, 'kind' => $giftId, 'deleted' => 0, 'nextStep' => $nextStep], $arrFields);

        return $afterSaleInfo;
    }

    // 通过pid查询信息
    public function getInfoByPid($pid, $arrFields = [])
    {
        if (intval($pid) <= 0) {
            Bd_Log::warning("[error] param error [Detail:pid - {$pid} ]");

            return false;
        }
        if ($arrFields == array()){
            $arrFields = Hkzb_Dao_Gnmis_AfterSale::$allFields;
        }
        $afterSaleInfo = $this->objDaoAfterSale->getListByConds(['aftersalePid' => $pid, 'deleted' => 0], $arrFields);

        return $afterSaleInfo;
    }

    // 通过指定条件获取列表
    public function getListByConds($arrConds, $arrFields)
    {
        if (!is_array($arrConds)) {
            Bd_Log::warning("[error] param error [Detail:arrConds - " . var_export($arrConds, true) . " arrFields - " . var_export($arrFields, true) . " ]");

            return false;
        }

        if ($arrFields == array()){
            $arrFields = Hkzb_Dao_Gnmis_AfterSale::$allFields;
        }
        $afterSaleInfo = $this->objDaoAfterSale->getListByConds($arrConds, $arrFields);

        return $afterSaleInfo;
    }

    // 通过orderId、type获取信息
    public function getInfoByOrderId($orderId, $type, $arrFields = [])
    {
        if (intval($orderId) <= 0) {
            Bd_Log::warning("[error] param error [Detail:pid - {$orderId}]");

            return false;
        }

        if ($arrFields == array()){
            $arrFields = Hkzb_Dao_Gnmis_AfterSale::$allFields;
        }

        return $this->objDaoAfterSale->getListByConds("order_id = {$orderId} AND deleted = 0 AND type in ({$type}) AND status = 1", $arrFields);
    }

    // 添加纪录
    public function addData($arrParam){
        $arrParam = array_intersect_key($arrParam,array_flip(Hkzb_Dao_Gnmis_AfterSale::$allFields));
        $re = $this->objDaoAfterSale->insertRecords($arrParam);
        if (!$re){
            Bd_Log::warning("[error] insert error [Detail:arrParam - ".var_export($arrParam,true)."]");
            return false;
        }

        return $this->objDaoAfterSale->getInsertId();
    }

    /**
     * 根据订单ids&售后type获取售后id
     *
     * @datetime 2018/5/9 15:42
     * <AUTHOR> <<EMAIL>>
     *
     * @param $orderIds       array 订单ids
     * @param $afterSaleType  int 售后类型
     */
    public function getAfterSaleIdByOrderIdsAndType($orderIds, $afterSaleType, $arrFields = null)
    {
        if (empty($orderIds)) {
            Bd_Log::warning("Error:[param error], Detail:[orderIds-" . @json_encode($orderIds) . "]");

            return false;
        };
        if (empty($afterSaleType)) {
            Bd_Log::warning("Error:[param error], Detail:[afterSale-" . $afterSaleType . "]");

            return false;
        };

        $orderIds = implode(',', $orderIds);

        $arrConds = ['order_id in (' . $orderIds . ')', 'type' => $afterSaleType, 'deleted' => 0];

        if (empty($arrFields)){
            $arrFields = Hkzb_Dao_Gnmis_AfterSale::$allFields;
        }

        $arrAppends = "ORDER BY id ASC";

        return $this->objDaoAfterSale->getListByConds($arrConds, $arrFields, null, $arrAppends);
    }

    /**
     * 根据订单id&售后type获取售后信息
     *
     * @datetime 2018/5/22 17:02
     * <AUTHOR> <<EMAIL>>
     *
     * @param $orderId        int 订单id
     * @param $afterSaleType  int 售后类型
     */
    public function getAfterSaleByOrderIdAndType($orderId, $afterSaleType, $arrFields = null)
    {
        if (empty($orderId)) {
            Bd_Log::warning("Error:[param error], Detail:[orderId-" . $orderId . "]");

            return false;
        };
        if (empty($afterSaleType)) {
            Bd_Log::warning("Error:[param error], Detail:[afterSale-" . $afterSaleType . "]");

            return false;
        };

        $arrConds = ['order_id' => $orderId, 'type' => $afterSaleType, 'deleted' => 0];

        if (empty($arrFields)){
            $arrFields = Hkzb_Dao_Gnmis_AfterSale::$allFields;
        }

        $arrAppends = "ORDER BY id ASC";

        return $this->objDaoAfterSale->getListByConds($arrConds, $arrFields, null, $arrAppends);
    }

    /**
     * 获取售后详情 - 通过refundId
     *
     * @datetime 2018/5/22 17:54
     *
     * @param   int      $refundId 退款id
     * @param null|array $arrFields   字段信息
     *
     * @return array|bool|false
     */
    public function getAfterSaleInfoByRefundId($refundId, $arrFields = null)
    {
        if (intval($refundId) <= 0) {
            Bd_Log::warning("[error] param is error [Detail:refundId-{$refundId}]");

            return false;
        }

        if (empty($arrFields)) {
            $arrFields = explode(",", self::ALL_FIELDS);
        }

        // 获取售后信息
        $afterSaleInfo = $this->objDaoAfterSale->getRecordByConds(['refundId' => $refundId, 'deleted' => 0], $arrFields);
        if (!$afterSaleInfo) {
            Bd_Log::warning("[error] select afterSaleInfo is error [Detail:refundId-{$refundId}]");

            return false;
        }

        return $afterSaleInfo;
    }
}
