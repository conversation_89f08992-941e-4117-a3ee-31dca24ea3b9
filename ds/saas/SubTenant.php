<?php
/**
 * @author: liming05<<EMAIL>>
 * @date: 2019/12/9
 * @desc:
 */

class Saaslib_Ds_Saas_SubTenant {
	/**
	 * @var Saaslib_Dao_Saas_SubTenant
	 */
	private $daoIns;

	/**
	 * Saaslib_Ds_Saas_SubTenant constructor.
	 */
	private function __construct() {
		$this->daoIns = new Saaslib_Dao_Saas_SubTenant();
	}

	/**
	 * @var Saaslib_Ds_Saas_SubTenant
	 */
	private static $instance = null;

	/**
	 * @return Saaslib_Ds_Saas_SubTenant
	 */
	public static function getInstance() {
		if (self::$instance == null) {
			self::$instance = new self();
		}
		return self::$instance;
	}

	/**
	 *
	 */
	private function __clone() {

	}

	/**
	 * @param $appId string
	 * @return array
	 * @throws Hk_Util_Exception
	 */
	public function getInfoByAppId($appId) {
		$objRedis = Hk_Service_RedisClient::getInstance('saascommon');
		$key = 'Saaslib_Ds_Saas_SubTenant_AppId_' . $appId;
		$redisRet = $objRedis->get($key);
		$redisRet = json_decode($redisRet, true);
		if (is_array($redisRet) && !empty($redisRet)) {
			return $redisRet;
		}
		$info = $this->daoIns->getInfoByAppId($appId);
		if(!empty($info)) {
			$strData = json_encode($info);
			$objRedis->setEx($key, 3600, $strData);
		}
		return $info;


	}
}