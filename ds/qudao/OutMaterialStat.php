<?php

class Qdlib_Ds_Qudao_OutMaterialStat extends Qdlib_Ds_Qudao_OutBase
{

    protected $_tableDaoObj;

    public function __construct()
    {
        parent::__construct('Qdlib_Dao_Qudao_OutMaterialStat');
    }

    protected function getFormatDbData($arrParams) {
        $arrFields = array(
            'uniqueId'                  => isset($arrParams['uniqueId']) ? strval($arrParams['uniqueId']) : '',
            'dt'                        => isset($arrParams['dt']) ? intval($arrParams['dt']) : 0,
            'dtHour'                    => isset($arrParams['dtHour']) ? intval($arrParams['dtHour']) : -1,
            'flag'                      => isset($arrParams['flag']) ? strval($arrParams['flag']) : '',
            'channel'                   => isset($arrParams['channel']) ? strval($arrParams['channel']) : '',
            'account'                   => isset($arrParams['account']) ? strval($arrParams['account']) : '',
            'appCode'                   => isset($arrParams['appCode']) ? strval($arrParams['appCode']) : '',
            'thirdAdId'                 => isset($arrParams['thirdAdId']) ? intval($arrParams['thirdAdId']) : 0,
            'thirdCampaignId'           => isset($arrParams['thirdCampaignId']) ? intval($arrParams['thirdCampaignId']) : 0,
            'thirdAdgroupId'            => isset($arrParams['thirdAdgroupId']) ? intval($arrParams['thirdAdgroupId']) : 0,
            'thirdMaterialId'           => isset($arrParams['thirdMaterialId']) ? strval($arrParams['thirdMaterialId']) : '',
            'showPv'                    => isset($arrParams['showPv']) ? intval($arrParams['showPv']) : 0,
            'clickPv'                   => isset($arrParams['clickPv']) ? intval($arrParams['clickPv']) : 0,
            'cost'                      => isset($arrParams['cost']) ? intval($arrParams['cost']) : 0,
            'conversion'                => isset($arrParams['conversion']) ? intval($arrParams['conversion']) : 0,
            'ext'                       => isset($arrParams['ext']) ? strval($arrParams['ext']) : json_encode([]),
            'deleted'                   => isset($arrParams['deleted']) ? intval($arrParams['deleted']) :0,
            'reportStatus'              => isset($arrParams['reportStatus']) ? intval($arrParams['reportStatus']) :0,
        );
        return $arrFields;
    }
}